import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import geopandas as gpd
import pandas as pd
import os
from shapely.geometry import Polygon, MultiPolygon
from shapely.ops import unary_union
import threading

DEFAULT_BOUNDARY_PATH = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
DEFAULT_POINTS_PATH = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"

class ARECDetectionResultGUI:
    def __init__(self, root):
        self.root = root
        self.root.title('ARE C Detection Result - Tree Count & Area Update')
        self.root.geometry('1200x700')
        self.boundary_path = DEFAULT_BOUNDARY_PATH
        self.points_path = DEFAULT_POINTS_PATH
        self.gdf_boundary = None
        self.gdf_points = None
        self.df_summary = None
        self.processed_gdf = None
        self.create_gui()
        self.load_data()

    def create_gui(self):
        f = ttk.Frame(self.root)
        f.pack(fill='both', expand=True)
        pad = {'padx': 8, 'pady': 4}
        # Path boundary
        ttk.Label(f, text='Boundary Polygon Shapefile:').grid(row=0, column=0, sticky='w', **pad)
        self.boundary_entry = ttk.Entry(f, width=90)
        self.boundary_entry.grid(row=0, column=1, **pad)
        self.boundary_entry.insert(0, self.boundary_path)
        ttk.Button(f, text='Browse', command=self.browse_boundary).grid(row=0, column=2, **pad)
        # Path points
        ttk.Label(f, text='Detection Points Shapefile:').grid(row=1, column=0, sticky='w', **pad)
        self.points_entry = ttk.Entry(f, width=90)
        self.points_entry.grid(row=1, column=1, **pad)
        self.points_entry.insert(0, self.points_path)
        ttk.Button(f, text='Browse', command=self.browse_points).grid(row=1, column=2, **pad)
        # Proses & Simpan
        ttk.Button(f, text='Proses Analisis', command=self.start_analysis).grid(row=2, column=1, sticky='e', **pad)
        ttk.Button(f, text='Simpan Shapefile Hasil', command=self.save_shapefile).grid(row=2, column=2, sticky='w', **pad)
        # Table
        columns = ('BLOK', 'SUBDIVISI', 'HCV', 'luas_total', 'luas_inclave', 'luas_netto', 'jumlah_pohon')
        self.tree = ttk.Treeview(f, columns=columns, show='headings', height=25)
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120)
        self.tree.grid(row=3, column=0, columnspan=3, sticky='nsew', **pad)
        vsb = ttk.Scrollbar(f, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=vsb.set)
        vsb.grid(row=3, column=3, sticky='ns')
        # Log
        self.log_text = scrolledtext.ScrolledText(f, height=8, state='disabled')
        self.log_text.grid(row=4, column=0, columnspan=4, sticky='ew', **pad)
        # Status
        self.status_var = tk.StringVar(value='Ready')
        ttk.Label(f, textvariable=self.status_var).grid(row=5, column=0, columnspan=2, sticky='w', **pad)
        f.rowconfigure(3, weight=1)
        f.columnconfigure(1, weight=1)

    def log(self, msg):
        self.log_text.configure(state='normal')
        self.log_text.insert('end', msg + '\n')
        self.log_text.see('end')
        self.log_text.configure(state='disabled')

    def browse_boundary(self):
        path = filedialog.askopenfilename(filetypes=[('Shapefile', '*.shp')])
        if path:
            self.boundary_path = path
            self.boundary_entry.delete(0, 'end')
            self.boundary_entry.insert(0, path)
            self.load_data()

    def browse_points(self):
        path = filedialog.askopenfilename(filetypes=[('Shapefile', '*.shp')])
        if path:
            self.points_path = path
            self.points_entry.delete(0, 'end')
            self.points_entry.insert(0, path)
            self.load_data()

    def load_data(self):
        self.log('Loading data...')
        try:
            self.gdf_boundary = gpd.read_file(self.boundary_path)
            self.gdf_points = gpd.read_file(self.points_path)
            self.status_var.set('Data loaded')
            self.log(f'Loaded {len(self.gdf_boundary)} polygons, {len(self.gdf_points)} points')
        except Exception as e:
            self.status_var.set('Error loading data')
            self.log(f'Error loading data: {e}')

    def start_analysis(self):
        t = threading.Thread(target=self.run_analysis)
        t.daemon = True
        t.start()

    def run_analysis(self):
        try:
            self.status_var.set('Processing...')
            self.log('Starting spatial overlay and area analysis...')
            gdf = self.gdf_boundary.copy()
            points = self.gdf_points.copy()
            # CRS
            if gdf.crs != points.crs:
                self.log('CRS mismatch, reprojecting points...')
                points = points.to_crs(gdf.crs)
            # HCV
            gdf['HCV'] = gdf['HCV'].astype(str)
            # Area calculation (projected)
            if not gdf.crs or not gdf.crs.is_projected:
                gdf = gdf.to_crs(epsg=32748)
            # Pisahkan boundary & enclave
            gdf0 = gdf[gdf['HCV'].isin(['0', 0])].copy()
            gdf1 = gdf[gdf['HCV'].isin(['1', 1])].copy()
            # Area
            gdf0['luas_total'] = gdf0.geometry.area / 10000
            gdf1['luas_inclave'] = gdf1.geometry.area / 10000
            # Group area
            luas_total = gdf0.groupby(['BLOK','SUBDIVISI'])['luas_total'].sum().reset_index()
            luas_inclave = gdf1.groupby(['BLOK','SUBDIVISI'])['luas_inclave'].sum().reset_index()
            df_sum = pd.merge(luas_total, luas_inclave, on=['BLOK','SUBDIVISI'], how='left').fillna({'luas_inclave':0})
            df_sum['luas_netto'] = df_sum['luas_total'] - df_sum['luas_inclave']
            # Overlay pohon
            join = gpd.sjoin(points, gdf0, how='inner', predicate='within')
            pohon_count = join.groupby(['BLOK','SUBDIVISI']).size().reset_index(name='jumlah_pohon')
            df_sum = pd.merge(df_sum, pohon_count, on=['BLOK','SUBDIVISI'], how='left').fillna({'jumlah_pohon':0})
            # Update atribut di boundary
            for idx, row in df_sum.iterrows():
                mask = (gdf['BLOK']==row['BLOK']) & (gdf['SUBDIVISI']==row['SUBDIVISI']) & (gdf['HCV'].isin(['0', 0]))
                gdf.loc[mask, 'luas_total'] = row['luas_total']
                gdf.loc[mask, 'luas_inclave'] = row['luas_inclave']
                gdf.loc[mask, 'luas_netto'] = row['luas_netto']
                gdf.loc[mask, 'jumlah_pohon'] = int(row['jumlah_pohon'])
            # --- Pembuatan Hole ---
            self.log('Membuat hole pada boundary jika ada enclave di dalamnya...')
            gdf = self.create_holes(gdf)
            self.processed_gdf = gdf
            self.df_summary = df_sum
            self.update_table()
            self.status_var.set('Analysis complete')
            self.log('Analysis complete.')
        except Exception as e:
            self.status_var.set('Error during analysis')
            self.log(f'Error during analysis: {e}')

    def create_holes(self, gdf):
        gdf = gdf.copy()
        for (blok, subdiv), group in gdf.groupby(['BLOK','SUBDIVISI']):
            boundary = group[(group['HCV'].isin(['0', 0]))]
            enclaves = group[(group['HCV'].isin(['1', 1]))]
            if len(boundary)==0 or len(enclaves)==0:
                continue
            enclaves_union = unary_union(enclaves.geometry)
            for idx, row in boundary.iterrows():
                geom = row.geometry
                if enclaves_union.is_empty:
                    continue
                # Buat hole jika enclave contained
                if geom.contains(enclaves_union):
                    new_geom = Polygon(geom.exterior.coords, [p.exterior.coords for p in getattr(enclaves_union, 'geoms', [enclaves_union])])
                    gdf.at[idx, 'geometry'] = new_geom
        return gdf

    def update_table(self):
        self.tree.delete(*self.tree.get_children())
        if self.df_summary is not None:
            for _, row in self.df_summary.iterrows():
                self.tree.insert('', 'end', values=(row['BLOK'], row['SUBDIVISI'], '0', f"{row['luas_total']:.2f}", f"{row['luas_inclave']:.2f}", f"{row['luas_netto']:.2f}", int(row['jumlah_pohon'])))

    def save_shapefile(self):
        if self.processed_gdf is None:
            messagebox.showerror('Error', 'No processed data to save!')
            return
        path = filedialog.asksaveasfilename(defaultextension='.shp', filetypes=[('Shapefile','*.shp')])
        if path:
            self.processed_gdf.to_file(path)
            self.log(f'Shapefile saved to {path}')

if __name__ == '__main__':
    root = tk.Tk()
    app = ARECDetectionResultGUI(root)
    root.mainloop() 
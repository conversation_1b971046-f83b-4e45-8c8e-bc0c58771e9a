@echo off
echo ========================================
echo ARE C Advanced Spatial Analyzer
echo ========================================
echo.
echo Starting application...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Install requirements if needed
if exist requirements_advanced_analyzer.txt (
    echo Installing/updating required packages...
    pip install -r requirements_advanced_analyzer.txt
    echo.
)

REM Run the application
echo Launching ARE C Advanced Spatial Analyzer...
python are_c_advanced_spatial_analyzer.py

echo.
echo Application closed.
pause

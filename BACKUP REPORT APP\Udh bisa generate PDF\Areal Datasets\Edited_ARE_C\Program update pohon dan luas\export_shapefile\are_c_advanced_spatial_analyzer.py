"""
ARE C Advanced Spatial Analyzer
Comprehensive GUI application for palm tree detection analysis with advanced spatial processing capabilities.

Features:
- Spatial overlay analysis (point-in-polygon)
- HCV category processing (boundary vs unplanted areas)
- Advanced polygon hole creation
- Net plantable area calculations
- Real-time progress tracking
- Comprehensive data export

Author: Generated for ARE C Analysis Project
Date: 2025-07-03
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import geopandas as gpd
import pandas as pd
import os
import logging
from datetime import datetime
import threading

class ARECAdvancedSpatialAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title('ARE C Advanced Spatial Analyzer - Palm Tree Detection Analysis')
        self.root.geometry('1400x900')
        self.root.minsize(1200, 800)
        
        # Fixed data paths as specified
        self.boundary_shapefile = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
        self.detection_points = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"
        
        # Data storage
        self.gdf_boundary = None
        self.gdf_points = None
        self.processed_data = None
        self.analysis_results = None
        
        # Processing flags
        self.processing_active = False
        self.cancel_processing = False
        
        # Setup logging
        self.setup_logging()
        
        # Create GUI
        self.create_gui()
        
        # Load initial data
        self.load_initial_data()
    
    def setup_logging(self):
        """Setup logging for debugging and audit trails"""
        log_dir = os.path.join(os.path.dirname(__file__), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        log_file = os.path.join(log_dir, f'are_c_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("ARE C Advanced Spatial Analyzer initialized")
    
    def create_gui(self):
        """Create the main GUI interface"""
        # Create main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_data_tab()
        self.create_analysis_tab()
        self.create_results_tab()
        self.create_export_tab()
        self.create_log_tab()
    
    def create_data_tab(self):
        """Create data management tab"""
        self.data_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_frame, text='Data Management')
        
        # File paths section
        paths_frame = ttk.LabelFrame(self.data_frame, text='Data Sources', padding=10)
        paths_frame.pack(fill='x', padx=10, pady=5)
        
        # Boundary shapefile
        ttk.Label(paths_frame, text='Boundary Polygon Shapefile:').grid(row=0, column=0, sticky='w', pady=2)
        self.boundary_var = tk.StringVar(value=self.boundary_shapefile)
        boundary_entry = ttk.Entry(paths_frame, textvariable=self.boundary_var, width=80)
        boundary_entry.grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(paths_frame, text='Browse', command=self.browse_boundary).grid(row=0, column=2, padx=5, pady=2)
        
        # Detection points shapefile
        ttk.Label(paths_frame, text='Detection Points Shapefile:').grid(row=1, column=0, sticky='w', pady=2)
        self.points_var = tk.StringVar(value=self.detection_points)
        points_entry = ttk.Entry(paths_frame, textvariable=self.points_var, width=80)
        points_entry.grid(row=1, column=1, padx=5, pady=2)
        ttk.Button(paths_frame, text='Browse', command=self.browse_points).grid(row=1, column=2, padx=5, pady=2)
        
        # Data validation section
        validation_frame = ttk.LabelFrame(self.data_frame, text='Data Validation', padding=10)
        validation_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Validation results display
        self.validation_text = scrolledtext.ScrolledText(validation_frame, height=15, width=80)
        self.validation_text.pack(fill='both', expand=True)
        
        # Control buttons
        control_frame = ttk.Frame(self.data_frame)
        control_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(control_frame, text='Validate Data', command=self.validate_data).pack(side='left', padx=5)
        ttk.Button(control_frame, text='Reload Data', command=self.load_initial_data).pack(side='left', padx=5)
        ttk.Button(control_frame, text='Clear Log', command=self.clear_validation_log).pack(side='left', padx=5)
    
    def browse_boundary(self):
        """Browse for boundary shapefile"""
        filename = filedialog.askopenfilename(
            title="Select Boundary Shapefile",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if filename:
            self.boundary_var.set(filename)
            self.boundary_shapefile = filename
    
    def browse_points(self):
        """Browse for detection points shapefile"""
        filename = filedialog.askopenfilename(
            title="Select Detection Points Shapefile",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )
        if filename:
            self.points_var.set(filename)
            self.detection_points = filename
    
    def clear_validation_log(self):
        """Clear validation log display"""
        self.validation_text.delete(1.0, tk.END)
    
    def log_to_validation(self, message):
        """Log message to validation text widget"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.validation_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.validation_text.see(tk.END)
        self.root.update_idletasks()
    
    def load_initial_data(self):
        """Load initial data from shapefiles"""
        self.log_to_validation("Loading initial data...")
        
        try:
            # Check if files exist
            if not os.path.exists(self.boundary_shapefile):
                self.log_to_validation(f"ERROR: Boundary shapefile not found: {self.boundary_shapefile}")
                return
            
            if not os.path.exists(self.detection_points):
                self.log_to_validation(f"ERROR: Detection points shapefile not found: {self.detection_points}")
                return
            
            # Load boundary shapefile
            self.log_to_validation("Loading boundary polygons...")
            self.gdf_boundary = gpd.read_file(self.boundary_shapefile)
            self.log_to_validation(f"✓ Loaded {len(self.gdf_boundary)} boundary polygons")
            self.log_to_validation(f"  CRS: {self.gdf_boundary.crs}")
            self.log_to_validation(f"  Columns: {list(self.gdf_boundary.columns)}")
            
            # Load detection points
            self.log_to_validation("Loading detection points...")
            self.gdf_points = gpd.read_file(self.detection_points)
            self.log_to_validation(f"✓ Loaded {len(self.gdf_points)} detection points")
            self.log_to_validation(f"  CRS: {self.gdf_points.crs}")
            self.log_to_validation(f"  Columns: {list(self.gdf_points.columns)}")
            
            # Check CRS compatibility
            if self.gdf_boundary.crs != self.gdf_points.crs:
                self.log_to_validation(f"WARNING: CRS mismatch detected")
                self.log_to_validation(f"  Boundary CRS: {self.gdf_boundary.crs}")
                self.log_to_validation(f"  Points CRS: {self.gdf_points.crs}")
                self.log_to_validation("  Will auto-convert during processing")
            else:
                self.log_to_validation(f"✓ CRS compatible: {self.gdf_boundary.crs}")
            
            self.log_to_validation("Initial data loading completed successfully")

        except Exception as e:
            self.log_to_validation(f"ERROR loading initial data: {str(e)}")
            self.logger.error(f"Error loading initial data: {str(e)}")

    def standardize_column_names(self):
        """Standardize column names to match expected format"""
        self.log_to_validation("Standardizing column names...")

        # Column name mappings for boundary data
        boundary_mappings = {
            'SUB_DIVISI': 'SUBDIVISI',
            'HCV_Catego': 'HCV',
            'Jumlah_Poh': 'JUMLAH_POH'
        }

        # Apply mappings to boundary data
        for old_name, new_name in boundary_mappings.items():
            if old_name in self.gdf_boundary.columns and new_name not in self.gdf_boundary.columns:
                self.gdf_boundary = self.gdf_boundary.rename(columns={old_name: new_name})
                self.log_to_validation(f"  Renamed '{old_name}' to '{new_name}' in boundary data")

        # Column name mappings for points data (if needed)
        points_mappings = {
            'SUBDIVISIO': 'SUBDIVISI',
            'BLOCK': 'BLOK'
        }

        # Apply mappings to points data
        for old_name, new_name in points_mappings.items():
            if old_name in self.gdf_points.columns and new_name not in self.gdf_points.columns:
                self.gdf_points = self.gdf_points.rename(columns={old_name: new_name})
                self.log_to_validation(f"  Renamed '{old_name}' to '{new_name}' in points data")

        # Clean and standardize data types
        self.clean_data_types()

        self.log_to_validation("Column name standardization completed")

    def clean_data_types(self):
        """Clean and standardize data types for numeric columns"""
        self.log_to_validation("Cleaning data types...")

        # Clean numeric columns in boundary data
        numeric_columns = ['JUMLAH_POH', 'LUAS_AUTO', 'luas_aut_1', 'total_incl', 'luas_netto', 'luas_asss']

        for col in numeric_columns:
            if col in self.gdf_boundary.columns:
                try:
                    # Convert to numeric, replacing non-numeric values with 0
                    original_values = self.gdf_boundary[col].copy()
                    self.gdf_boundary[col] = pd.to_numeric(self.gdf_boundary[col], errors='coerce').fillna(0)

                    # Count conversions
                    non_numeric_count = original_values.isna().sum() if original_values.dtype == 'object' else 0
                    if non_numeric_count > 0:
                        self.log_to_validation(f"  Cleaned {col}: {non_numeric_count} non-numeric values converted to 0")
                    else:
                        self.log_to_validation(f"  Verified {col}: numeric data type")

                except Exception as e:
                    self.log_to_validation(f"  Warning: Could not clean column {col}: {str(e)}")

        # Ensure HCV column is string type for pattern matching
        if 'HCV' in self.gdf_boundary.columns:
            self.gdf_boundary['HCV'] = self.gdf_boundary['HCV'].astype(str)
            self.log_to_validation("  Converted HCV to string type for pattern matching")

        self.log_to_validation("Data type cleaning completed")

    def validate_data(self):
        """Validate loaded data for processing"""
        self.log_to_validation("=== DATA VALIDATION ===")

        if self.gdf_boundary is None or self.gdf_points is None:
            self.log_to_validation("ERROR: Data not loaded. Please load data first.")
            return False

        # Validate boundary data
        self.log_to_validation("Validating boundary data...")

        # Check for column name variations and standardize
        self.standardize_column_names()

        required_boundary_cols = ['BLOK', 'SUBDIVISI', 'HCV', 'geometry']
        missing_cols = [col for col in required_boundary_cols if col not in self.gdf_boundary.columns]

        if missing_cols:
            self.log_to_validation(f"ERROR: Missing required columns in boundary data: {missing_cols}")
            self.log_to_validation(f"Available columns: {list(self.gdf_boundary.columns)}")
            return False

        # Check HCV categories
        hcv_values = self.gdf_boundary['HCV'].unique()
        self.log_to_validation(f"HCV categories found: {hcv_values}")

        # Handle different HCV value formats
        # Your data appears to use "Boundary-XX" format, so we'll treat all as boundaries for now
        boundary_count = len(self.gdf_boundary[self.gdf_boundary['HCV'].str.contains('Boundary', na=False)])
        inclave_count = len(self.gdf_boundary[self.gdf_boundary['HCV'].str.contains('inclave', case=False, na=False)])

        self.log_to_validation(f"  Boundary polygons: {boundary_count}")
        self.log_to_validation(f"  Inclave polygons: {inclave_count}")

        # If no inclave found, check for other patterns
        if inclave_count == 0:
            self.log_to_validation("  Note: No 'inclave' patterns found. All polygons will be treated as boundaries.")
            self.log_to_validation("  If you have unplanted areas, they should contain 'inclave' in the HCV field.")

        # Validate points data
        self.log_to_validation("Validating detection points...")
        if 'geometry' not in self.gdf_points.columns:
            self.log_to_validation("ERROR: No geometry column in points data")
            return False

        # Check for valid geometries
        invalid_boundary = self.gdf_boundary[~self.gdf_boundary.geometry.is_valid]
        invalid_points = self.gdf_points[~self.gdf_points.geometry.is_valid]

        if len(invalid_boundary) > 0:
            self.log_to_validation(f"WARNING: {len(invalid_boundary)} invalid boundary geometries found")

        if len(invalid_points) > 0:
            self.log_to_validation(f"WARNING: {len(invalid_points)} invalid point geometries found")

        # Summary by Block and Sub Division
        self.log_to_validation("\n=== SUMMARY BY BLOCK/SUBDIVISION ===")
        summary = self.gdf_boundary.groupby(['BLOK', 'SUBDIVISI', 'HCV']).size().reset_index(name='count')

        for _, row in summary.iterrows():
            hcv_value = str(row['HCV'])
            if 'Boundary' in hcv_value:
                hcv_type = "Boundary"
            elif 'inclave' in hcv_value.lower():
                hcv_type = "Inclave/Unplanted"
            else:
                hcv_type = "Other"
            self.log_to_validation(f"  {row['BLOK']} - {row['SUBDIVISI']} ({hcv_type}): {row['count']} polygons")

        # Check if there's existing tree count data
        if 'JUMLAH_POH' in self.gdf_boundary.columns:
            try:
                # Convert to numeric, handling mixed data types
                numeric_trees = pd.to_numeric(self.gdf_boundary['JUMLAH_POH'], errors='coerce')
                existing_trees = numeric_trees.sum()
                non_numeric_count = numeric_trees.isna().sum()

                self.log_to_validation(f"\nExisting tree count in data: {existing_trees}")
                if non_numeric_count > 0:
                    self.log_to_validation(f"Warning: {non_numeric_count} non-numeric values found in JUMLAH_POH column")
            except Exception as e:
                self.log_to_validation(f"\nError reading existing tree count data: {str(e)}")
        else:
            self.log_to_validation("\nNo existing tree count data found")

        self.log_to_validation("✓ Data validation completed")
        return True

    def create_analysis_tab(self):
        """Create spatial analysis tab"""
        self.analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analysis_frame, text='Spatial Analysis')

        # Analysis options
        options_frame = ttk.LabelFrame(self.analysis_frame, text='Analysis Options', padding=10)
        options_frame.pack(fill='x', padx=10, pady=5)

        # Processing options
        self.overlay_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text='Perform Spatial Overlay Analysis', variable=self.overlay_var).grid(row=0, column=0, sticky='w', pady=2)

        self.hcv_processing_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text='Process HCV Categories', variable=self.hcv_processing_var).grid(row=1, column=0, sticky='w', pady=2)

        self.hole_creation_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text='Create Polygon Holes (Advanced)', variable=self.hole_creation_var).grid(row=2, column=0, sticky='w', pady=2)

        self.area_calculation_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text='Calculate Net Plantable Areas', variable=self.area_calculation_var).grid(row=3, column=0, sticky='w', pady=2)

        # Progress section
        progress_frame = ttk.LabelFrame(self.analysis_frame, text='Processing Progress', padding=10)
        progress_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill='x', pady=5)

        # Status label
        self.status_var = tk.StringVar(value="Ready for analysis")
        ttk.Label(progress_frame, textvariable=self.status_var).pack(pady=2)

        # Processing log
        self.processing_log = scrolledtext.ScrolledText(progress_frame, height=20, width=80)
        self.processing_log.pack(fill='both', expand=True, pady=5)

        # Control buttons
        control_frame = ttk.Frame(self.analysis_frame)
        control_frame.pack(fill='x', padx=10, pady=5)

        self.start_btn = ttk.Button(control_frame, text='Start Analysis', command=self.start_analysis)
        self.start_btn.pack(side='left', padx=5)

        self.cancel_btn = ttk.Button(control_frame, text='Cancel', command=self.cancel_analysis, state='disabled')
        self.cancel_btn.pack(side='left', padx=5)

        ttk.Button(control_frame, text='Clear Log', command=self.clear_processing_log).pack(side='left', padx=5)

    def log_to_processing(self, message):
        """Log message to processing text widget"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.processing_log.insert(tk.END, f"[{timestamp}] {message}\n")
        self.processing_log.see(tk.END)
        self.root.update_idletasks()

    def clear_processing_log(self):
        """Clear processing log display"""
        self.processing_log.delete(1.0, tk.END)

    def update_progress(self, value, status=""):
        """Update progress bar and status"""
        self.progress_var.set(value)
        if status:
            self.status_var.set(status)
        self.root.update_idletasks()

    def start_analysis(self):
        """Start spatial analysis in separate thread"""
        if not self.validate_data():
            messagebox.showerror("Validation Error", "Data validation failed. Please check the data and try again.")
            return

        # Disable start button, enable cancel
        self.start_btn.config(state='disabled')
        self.cancel_btn.config(state='normal')
        self.processing_active = True
        self.cancel_processing = False

        # Start analysis in separate thread
        analysis_thread = threading.Thread(target=self.run_spatial_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def cancel_analysis(self):
        """Cancel ongoing analysis"""
        self.cancel_processing = True
        self.log_to_processing("Analysis cancellation requested...")
        self.status_var.set("Cancelling...")

    def run_spatial_analysis(self):
        """Run the complete spatial analysis process"""
        try:
            self.log_to_processing("=== STARTING SPATIAL ANALYSIS ===")
            self.update_progress(0, "Initializing analysis...")

            # Step 1: Prepare data
            if self.cancel_processing:
                return

            self.log_to_processing("Step 1: Preparing data...")
            self.update_progress(10, "Preparing data...")

            # Ensure CRS compatibility
            if self.gdf_boundary.crs != self.gdf_points.crs:
                self.log_to_processing(f"Converting points CRS: {self.gdf_points.crs} -> {self.gdf_boundary.crs}")
                self.gdf_points = self.gdf_points.to_crs(self.gdf_boundary.crs)

            # Step 2: Spatial overlay analysis
            if self.overlay_var.get() and not self.cancel_processing:
                self.log_to_processing("Step 2: Performing spatial overlay analysis...")
                self.update_progress(20, "Spatial overlay analysis...")
                self.perform_spatial_overlay()

            # Step 3: HCV category processing
            if self.hcv_processing_var.get() and not self.cancel_processing:
                self.log_to_processing("Step 3: Processing HCV categories...")
                self.update_progress(50, "Processing HCV categories...")
                self.process_hcv_categories()

            # Step 4: Area calculations
            if self.area_calculation_var.get() and not self.cancel_processing:
                self.log_to_processing("Step 4: Calculating areas...")
                self.update_progress(70, "Calculating areas...")
                self.calculate_areas()

            # Step 5: Polygon hole creation (if enabled)
            if self.hole_creation_var.get() and not self.cancel_processing:
                self.log_to_processing("Step 5: Creating polygon holes...")
                self.update_progress(85, "Creating polygon holes...")
                self.create_polygon_holes()

            # Step 6: Finalize results
            if not self.cancel_processing:
                self.log_to_processing("Step 6: Finalizing results...")
                self.update_progress(95, "Finalizing results...")
                self.finalize_analysis()

                self.update_progress(100, "Analysis completed successfully!")
                self.log_to_processing("=== ANALYSIS COMPLETED SUCCESSFULLY ===")

                # Show results tab
                self.notebook.select(2)  # Switch to results tab

        except Exception as e:
            self.log_to_processing(f"ERROR during analysis: {str(e)}")
            self.logger.error(f"Analysis error: {str(e)}")
            self.update_progress(0, "Analysis failed")
            messagebox.showerror("Analysis Error", f"Analysis failed: {str(e)}")

        finally:
            # Re-enable controls
            self.processing_active = False
            self.start_btn.config(state='normal')
            self.cancel_btn.config(state='disabled')

    def perform_spatial_overlay(self):
        """Perform point-in-polygon spatial overlay analysis"""
        self.log_to_processing("Performing spatial overlay (point-in-polygon)...")

        # Filter boundary polygons (use all boundary polygons for tree counting)
        # Since your data uses "Boundary-XX" format, we'll use all boundary polygons
        boundary_polygons = self.gdf_boundary[
            self.gdf_boundary['HCV'].str.contains('Boundary', na=False)
        ].copy()

        # If no boundary polygons found, use all polygons
        if len(boundary_polygons) == 0:
            self.log_to_processing("No 'Boundary' polygons found, using all polygons for tree counting")
            boundary_polygons = self.gdf_boundary.copy()

        self.log_to_processing(f"Using {len(boundary_polygons)} boundary polygons for tree counting")

        # Perform spatial join
        self.log_to_processing("Executing spatial join (sjoin with 'within' predicate)...")
        overlay_result = gpd.sjoin(self.gdf_points, boundary_polygons, how='inner', predicate='within')

        self.log_to_processing(f"Found {len(overlay_result)} detection points within boundary polygons")

        # Count trees per polygon
        tree_counts = overlay_result.groupby('index_right').size().reset_index(name='tree_count')

        # Merge back to boundary data
        boundary_polygons['poly_index'] = boundary_polygons.index
        boundary_with_counts = boundary_polygons.merge(
            tree_counts,
            left_on='poly_index',
            right_on='index_right',
            how='left'
        )

        # Fill missing values with 0
        boundary_with_counts['tree_count'] = boundary_with_counts['tree_count'].fillna(0).astype(int)

        # Update the main boundary dataset
        for _, row in boundary_with_counts.iterrows():
            original_idx = row['poly_index']
            self.gdf_boundary.loc[original_idx, 'JUMLAH_POH'] = row['tree_count']

        # Log results
        total_trees = boundary_with_counts['tree_count'].sum()
        self.log_to_processing(f"Total trees counted: {total_trees}")

        # Summary by block
        block_summary = boundary_with_counts.groupby('BLOK')['tree_count'].sum().reset_index()
        self.log_to_processing("Tree count by BLOK:")
        for _, row in block_summary.iterrows():
            self.log_to_processing(f"  {row['BLOK']}: {row['tree_count']} trees")

    def process_hcv_categories(self):
        """Process HCV categories for area calculations"""
        self.log_to_processing("Processing HCV categories...")

        # Group by Block and Sub Division
        grouped = self.gdf_boundary.groupby(['BLOK', 'SUBDIVISI'])

        results = []

        for (blok, subdivisi), group in grouped:
            if self.cancel_processing:
                break

            # Separate HCV categories based on actual data format
            # Main boundaries (contains "Boundary")
            hcv_boundaries = group[group['HCV'].str.contains('Boundary', na=False)]
            # Unplanted areas (contains "inclave" - case insensitive)
            hcv_inclave = group[group['HCV'].str.contains('inclave', case=False, na=False)]

            # Calculate areas
            total_area = hcv_boundaries.geometry.area.sum() / 10000  # Convert to hectares
            unplanted_area = hcv_inclave.geometry.area.sum() / 10000 if len(hcv_inclave) > 0 else 0
            net_area = total_area - unplanted_area

            # Get tree count (safely handle numeric conversion)
            if 'JUMLAH_POH' in hcv_boundaries.columns and len(hcv_boundaries) > 0:
                try:
                    tree_count = pd.to_numeric(hcv_boundaries['JUMLAH_POH'], errors='coerce').fillna(0).sum()
                except Exception:
                    tree_count = 0
            else:
                tree_count = 0

            result = {
                'BLOK': blok,
                'SUBDIVISI': subdivisi,
                'total_area_ha': total_area,
                'unplanted_area_ha': unplanted_area,
                'net_plantable_area_ha': net_area,
                'tree_count': tree_count,
                'hcv_0_polygons': len(hcv_boundaries),
                'hcv_1_polygons': len(hcv_inclave)
            }

            results.append(result)

            self.log_to_processing(f"  {blok}-{subdivisi}: {total_area:.2f}ha total, {unplanted_area:.2f}ha unplanted, {net_area:.2f}ha net, {tree_count} trees")

        # Store results
        self.analysis_results = pd.DataFrame(results)
        self.log_to_processing(f"Processed {len(results)} Block/Sub Division combinations")

    def calculate_areas(self):
        """Calculate and update area fields"""
        self.log_to_processing("Calculating polygon areas...")

        # Add area calculations to boundary data
        if 'luas_total' not in self.gdf_boundary.columns:
            self.gdf_boundary['luas_total'] = 0.0

        if 'luas_auto' not in self.gdf_boundary.columns:
            self.gdf_boundary['luas_auto'] = self.gdf_boundary.geometry.area / 10000  # Convert to hectares

        # Update luas_total field with calculated values
        self.gdf_boundary['luas_total'] = self.gdf_boundary['luas_auto']

        self.log_to_processing("Area calculations completed")

    def create_polygon_holes(self):
        """Create holes in larger polygons where smaller polygons are contained within them"""
        self.log_to_processing("Creating polygon holes (advanced geometric processing)...")

        # This is a complex operation - identify nested polygons
        # Use boundary polygons for hole creation
        boundary_polygons = self.gdf_boundary[
            self.gdf_boundary['HCV'].str.contains('Boundary', na=False)
        ].copy()

        holes_created = 0

        for idx, outer_poly in boundary_polygons.iterrows():
            if self.cancel_processing:
                break

            outer_geom = outer_poly.geometry

            # Find polygons that are completely within this polygon
            for inner_idx, inner_poly in boundary_polygons.iterrows():
                if idx == inner_idx:
                    continue

                inner_geom = inner_poly.geometry

                # Check if inner polygon is completely within outer polygon
                if outer_geom.contains(inner_geom):
                    try:
                        # Create hole by subtracting inner from outer
                        new_geom = outer_geom.difference(inner_geom)
                        self.gdf_boundary.loc[idx, 'geometry'] = new_geom
                        holes_created += 1

                        self.log_to_processing(f"  Created hole in polygon {idx} (removed polygon {inner_idx})")

                    except Exception as e:
                        self.log_to_processing(f"  Warning: Could not create hole for polygon {idx}: {str(e)}")

        self.log_to_processing(f"Polygon hole creation completed. {holes_created} holes created.")

    def finalize_analysis(self):
        """Finalize analysis results"""
        self.log_to_processing("Finalizing analysis results...")

        # Ensure all required columns exist
        required_cols = ['JUMLAH_POH', 'luas_total', 'luas_auto']
        for col in required_cols:
            if col not in self.gdf_boundary.columns:
                self.gdf_boundary[col] = 0

        # Store processed data
        self.processed_data = self.gdf_boundary.copy()

        self.log_to_processing("Analysis finalization completed")

    def create_results_tab(self):
        """Create results display tab"""
        self.results_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.results_frame, text='Results')

        # Summary statistics
        stats_frame = ttk.LabelFrame(self.results_frame, text='Analysis Summary', padding=10)
        stats_frame.pack(fill='x', padx=10, pady=5)

        self.stats_text = scrolledtext.ScrolledText(stats_frame, height=8, width=80)
        self.stats_text.pack(fill='both', expand=True)

        # Detailed results table
        table_frame = ttk.LabelFrame(self.results_frame, text='Detailed Results', padding=10)
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Create treeview for results
        columns = ('BLOK', 'SUBDIVISI', 'Total_Area_Ha', 'Unplanted_Area_Ha', 'Net_Area_Ha', 'Tree_Count', 'HCV0_Polygons', 'HCV1_Polygons')
        self.results_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configure columns
        for col in columns:
            self.results_tree.heading(col, text=col.replace('_', ' '))
            self.results_tree.column(col, width=120)

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.results_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Control buttons
        results_control_frame = ttk.Frame(self.results_frame)
        results_control_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(results_control_frame, text='Refresh Results', command=self.refresh_results).pack(side='left', padx=5)
        ttk.Button(results_control_frame, text='Export to Excel', command=self.export_to_excel).pack(side='left', padx=5)
        ttk.Button(results_control_frame, text='Export Shapefile', command=self.export_shapefile).pack(side='left', padx=5)

    def refresh_results(self):
        """Refresh results display"""
        if self.analysis_results is None:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, "No analysis results available. Please run analysis first.")
            return

        # Update summary statistics
        self.stats_text.delete(1.0, tk.END)

        total_area = self.analysis_results['total_area_ha'].sum()
        total_unplanted = self.analysis_results['unplanted_area_ha'].sum()
        total_net = self.analysis_results['net_plantable_area_ha'].sum()
        total_trees = self.analysis_results['tree_count'].sum()

        stats_text = f"""ANALYSIS SUMMARY
================
Total Area: {total_area:.2f} hectares
Total Unplanted Area: {total_unplanted:.2f} hectares
Total Net Plantable Area: {total_net:.2f} hectares
Total Trees Detected: {total_trees:,}

Average Trees per Hectare (Net): {total_trees/total_net:.1f} trees/ha
Unplanted Area Percentage: {(total_unplanted/total_area)*100:.1f}%

Number of Block/Sub Division combinations: {len(self.analysis_results)}
"""

        self.stats_text.insert(tk.END, stats_text)

        # Update results table
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        for _, row in self.analysis_results.iterrows():
            values = (
                row['BLOK'],
                row['SUBDIVISI'],
                f"{row['total_area_ha']:.2f}",
                f"{row['unplanted_area_ha']:.2f}",
                f"{row['net_plantable_area_ha']:.2f}",
                int(row['tree_count']),
                int(row['hcv_0_polygons']),
                int(row['hcv_1_polygons'])
            )
            self.results_tree.insert('', 'end', values=values)

    def create_export_tab(self):
        """Create data export tab"""
        self.export_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.export_frame, text='Export')

        # Export options
        options_frame = ttk.LabelFrame(self.export_frame, text='Export Options', padding=10)
        options_frame.pack(fill='x', padx=10, pady=5)

        # Export format options
        ttk.Label(options_frame, text='Export Formats:').grid(row=0, column=0, sticky='w', pady=2)

        self.export_excel_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text='Excel (.xlsx)', variable=self.export_excel_var).grid(row=1, column=0, sticky='w', padx=20)

        self.export_csv_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text='CSV (.csv)', variable=self.export_csv_var).grid(row=2, column=0, sticky='w', padx=20)

        self.export_shapefile_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text='Shapefile (.shp)', variable=self.export_shapefile_var).grid(row=3, column=0, sticky='w', padx=20)

        self.export_geojson_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text='GeoJSON (.geojson)', variable=self.export_geojson_var).grid(row=4, column=0, sticky='w', padx=20)

        # Output directory
        ttk.Label(options_frame, text='Output Directory:').grid(row=5, column=0, sticky='w', pady=(10,2))
        self.output_dir_var = tk.StringVar(value=os.path.dirname(__file__))
        ttk.Entry(options_frame, textvariable=self.output_dir_var, width=60).grid(row=6, column=0, columnspan=2, sticky='ew', padx=(20,5))
        ttk.Button(options_frame, text='Browse', command=self.browse_output_dir).grid(row=6, column=2, padx=5)

        # Export status
        status_frame = ttk.LabelFrame(self.export_frame, text='Export Status', padding=10)
        status_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.export_log = scrolledtext.ScrolledText(status_frame, height=15, width=80)
        self.export_log.pack(fill='both', expand=True)

        # Export buttons
        export_control_frame = ttk.Frame(self.export_frame)
        export_control_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(export_control_frame, text='Export All', command=self.export_all_formats).pack(side='left', padx=5)
        ttk.Button(export_control_frame, text='Export Summary Only', command=self.export_summary_only).pack(side='left', padx=5)
        ttk.Button(export_control_frame, text='Clear Log', command=self.clear_export_log).pack(side='left', padx=5)

    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir_var.set(directory)

    def clear_export_log(self):
        """Clear export log"""
        self.export_log.delete(1.0, tk.END)

    def log_to_export(self, message):
        """Log message to export text widget"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.export_log.insert(tk.END, f"[{timestamp}] {message}\n")
        self.export_log.see(tk.END)
        self.root.update_idletasks()

    def export_to_excel(self):
        """Export results to Excel"""
        if self.analysis_results is None:
            messagebox.showerror("Export Error", "No analysis results to export. Please run analysis first.")
            return

        filename = filedialog.asksaveasfilename(
            title="Save Excel File",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )

        if filename:
            try:
                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    self.analysis_results.to_excel(writer, sheet_name='Summary', index=False)

                    if self.processed_data is not None:
                        # Export detailed polygon data (without geometry)
                        detailed_data = self.processed_data.drop('geometry', axis=1)
                        detailed_data.to_excel(writer, sheet_name='Detailed_Data', index=False)

                messagebox.showinfo("Export Success", f"Data exported to:\n{filename}")
                self.log_to_export(f"Excel export successful: {filename}")

            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export Excel file:\n{str(e)}")
                self.log_to_export(f"Excel export failed: {str(e)}")

    def export_shapefile(self):
        """Export processed shapefile"""
        if self.processed_data is None:
            messagebox.showerror("Export Error", "No processed data to export. Please run analysis first.")
            return

        filename = filedialog.asksaveasfilename(
            title="Save Shapefile",
            defaultextension=".shp",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")]
        )

        if filename:
            try:
                self.processed_data.to_file(filename)
                messagebox.showinfo("Export Success", f"Shapefile exported to:\n{filename}")
                self.log_to_export(f"Shapefile export successful: {filename}")

            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export shapefile:\n{str(e)}")
                self.log_to_export(f"Shapefile export failed: {str(e)}")

    def export_all_formats(self):
        """Export data in all selected formats"""
        if self.analysis_results is None:
            messagebox.showerror("Export Error", "No analysis results to export. Please run analysis first.")
            return

        output_dir = self.output_dir_var.get()
        if not os.path.exists(output_dir):
            messagebox.showerror("Export Error", f"Output directory does not exist: {output_dir}")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"ARE_C_Analysis_Results_{timestamp}"

        self.log_to_export("Starting batch export...")

        try:
            # Export Excel
            if self.export_excel_var.get():
                excel_path = os.path.join(output_dir, f"{base_filename}.xlsx")
                with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                    self.analysis_results.to_excel(writer, sheet_name='Summary', index=False)
                    if self.processed_data is not None:
                        detailed_data = self.processed_data.drop('geometry', axis=1)
                        detailed_data.to_excel(writer, sheet_name='Detailed_Data', index=False)
                self.log_to_export(f"✓ Excel exported: {excel_path}")

            # Export CSV
            if self.export_csv_var.get():
                csv_path = os.path.join(output_dir, f"{base_filename}.csv")
                self.analysis_results.to_csv(csv_path, index=False)
                self.log_to_export(f"✓ CSV exported: {csv_path}")

            # Export Shapefile
            if self.export_shapefile_var.get() and self.processed_data is not None:
                shp_path = os.path.join(output_dir, f"{base_filename}.shp")
                self.processed_data.to_file(shp_path)
                self.log_to_export(f"✓ Shapefile exported: {shp_path}")

            # Export GeoJSON
            if self.export_geojson_var.get() and self.processed_data is not None:
                geojson_path = os.path.join(output_dir, f"{base_filename}.geojson")
                self.processed_data.to_file(geojson_path, driver='GeoJSON')
                self.log_to_export(f"✓ GeoJSON exported: {geojson_path}")

            self.log_to_export("Batch export completed successfully!")
            messagebox.showinfo("Export Success", f"All selected formats exported to:\n{output_dir}")

        except Exception as e:
            self.log_to_export(f"Batch export failed: {str(e)}")
            messagebox.showerror("Export Error", f"Batch export failed:\n{str(e)}")

    def export_summary_only(self):
        """Export summary results only"""
        if self.analysis_results is None:
            messagebox.showerror("Export Error", "No analysis results to export. Please run analysis first.")
            return

        filename = filedialog.asksaveasfilename(
            title="Save Summary File",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if filename:
            try:
                if filename.endswith('.xlsx'):
                    self.analysis_results.to_excel(filename, index=False)
                else:
                    self.analysis_results.to_csv(filename, index=False)

                messagebox.showinfo("Export Success", f"Summary exported to:\n{filename}")
                self.log_to_export(f"Summary export successful: {filename}")

            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export summary:\n{str(e)}")
                self.log_to_export(f"Summary export failed: {str(e)}")

    def create_log_tab(self):
        """Create system log tab"""
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text='System Log')

        # Log display
        log_display_frame = ttk.LabelFrame(self.log_frame, text='System Log', padding=10)
        log_display_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.system_log = scrolledtext.ScrolledText(log_display_frame, height=25, width=100)
        self.system_log.pack(fill='both', expand=True)

        # Log control buttons
        log_control_frame = ttk.Frame(self.log_frame)
        log_control_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(log_control_frame, text='Refresh Log', command=self.refresh_system_log).pack(side='left', padx=5)
        ttk.Button(log_control_frame, text='Clear Log', command=self.clear_system_log).pack(side='left', padx=5)
        ttk.Button(log_control_frame, text='Save Log', command=self.save_system_log).pack(side='left', padx=5)

        # Auto-refresh log
        self.refresh_system_log()

    def refresh_system_log(self):
        """Refresh system log display"""
        try:
            # Read log file if it exists
            log_dir = os.path.join(os.path.dirname(__file__), 'logs')
            if os.path.exists(log_dir):
                log_files = [f for f in os.listdir(log_dir) if f.startswith('are_c_analysis_') and f.endswith('.log')]
                if log_files:
                    # Get the most recent log file
                    latest_log = max(log_files, key=lambda x: os.path.getctime(os.path.join(log_dir, x)))
                    log_path = os.path.join(log_dir, latest_log)

                    with open(log_path, 'r', encoding='utf-8') as f:
                        log_content = f.read()

                    self.system_log.delete(1.0, tk.END)
                    self.system_log.insert(tk.END, log_content)
                    self.system_log.see(tk.END)
                else:
                    self.system_log.delete(1.0, tk.END)
                    self.system_log.insert(tk.END, "No log files found.")
            else:
                self.system_log.delete(1.0, tk.END)
                self.system_log.insert(tk.END, "Log directory not found.")

        except Exception as e:
            self.system_log.delete(1.0, tk.END)
            self.system_log.insert(tk.END, f"Error reading log file: {str(e)}")

    def clear_system_log(self):
        """Clear system log display"""
        self.system_log.delete(1.0, tk.END)

    def save_system_log(self):
        """Save system log to file"""
        filename = filedialog.asksaveasfilename(
            title="Save System Log",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if filename:
            try:
                log_content = self.system_log.get(1.0, tk.END)
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                messagebox.showinfo("Save Success", f"System log saved to:\n{filename}")
            except Exception as e:
                messagebox.showerror("Save Error", f"Failed to save log:\n{str(e)}")

def main():
    """Main function to run the application"""
    root = tk.Tk()
    ARECAdvancedSpatialAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main()

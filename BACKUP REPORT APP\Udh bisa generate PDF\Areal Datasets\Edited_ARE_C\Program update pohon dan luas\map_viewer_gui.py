import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import geopandas as gpd
import pandas as pd

class MapViewerGUI:
    def __init__(self, master):
        self.master = master
        master.title("Tree Count and Area Analysis")

        # Default paths
        default_polygon_path = "D:\\Gawean Rebinmas\\Tree Counting Project\\Training Tree Counter Sawit Current\\BACKUP REPORT APP\\Udh bisa generate PDF\\Areal Datasets\\Edited_ARE_C\\Program update pohon dan luas\\export_shapefile\\Polygon_ARE_C_dengan_Jumlah_Pohon_cleaned.shp"
        default_point_path = "D:\\Gawean Rebinmas\\Tree Counting Project\\Training Tree Counter Sawit Current\\BACKUP REPORT APP\\Udh bisa generate PDF\\Areal Datasets\\Edited_ARE_C\\Program update pohon dan luas\\export_shapefile\\titik_pohon.shp" # Ganti dengan path titik pohon Anda

        # --- GUI Elements ---
        path_frame = ttk.LabelFrame(master, text="File Paths")
        path_frame.pack(padx=10, pady=10, fill="x")

        # Polygon Path
        ttk.Label(path_frame, text="Boundary Shapefile:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.polygon_path_entry = ttk.Entry(path_frame, width=80)
        self.polygon_path_entry.grid(row=0, column=1, padx=5, pady=5)
        self.polygon_path_entry.insert(0, default_polygon_path)
        ttk.Button(path_frame, text="Browse", command=self.browse_polygon).grid(row=0, column=2, padx=5, pady=5)

        # Point Path
        ttk.Label(path_frame, text="Tree Points Shapefile:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.point_path_entry = ttk.Entry(path_frame, width=80)
        self.point_path_entry.grid(row=1, column=1, padx=5, pady=5)
        self.point_path_entry.insert(0, default_point_path)
        ttk.Button(path_frame, text="Browse", command=self.browse_point).grid(row=1, column=2, padx=5, pady=5)

        # Analysis Button
        analysis_frame = ttk.LabelFrame(master, text="Analysis")
        analysis_frame.pack(padx=10, pady=10, fill="x")
        self.run_button = ttk.Button(analysis_frame, text="Run Analysis and Save New File...", command=self.run_analysis)
        self.run_button.pack(pady=10)

        # Results Table
        results_frame = ttk.LabelFrame(master, text="Results")
        results_frame.pack(padx=10, pady=10, fill="both", expand=True)
        self.tree = ttk.Treeview(results_frame, columns=("Blok", "Sub Divisi", "Jml Pohon", "Luas Total (Ha)", "Luas Inclave (Ha)", "Luas Netto (Ha)"), show="headings")
        for col in self.tree["columns"]:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120)
        self.tree.pack(fill="both", expand=True)

    def browse_polygon(self):
        path = filedialog.askopenfilename(filetypes=[("Shapefiles", "*.shp")])
        if path:
            self.polygon_path_entry.delete(0, tk.END)
            self.polygon_path_entry.insert(0, path)

    def browse_point(self):
        path = filedialog.askopenfilename(filetypes=[("Shapefiles", "*.shp")])
        if path:
            self.point_path_entry.delete(0, tk.END)
            self.point_path_entry.insert(0, path)

    def run_analysis(self):
        polygon_path = self.polygon_path_entry.get()
        point_path = self.point_path_entry.get()

        if not polygon_path or not point_path:
            messagebox.showerror("Error", "Please provide paths for both shapefiles.")
            return

        # Ask user for a new file path to save the results
        output_path = filedialog.asksaveasfilename(
            defaultextension=".shp",
            filetypes=[("ESRI Shapefile", "*.shp")],
            title="Save Analysis Result As"
        )

        # If the user cancels the save dialog, stop the analysis
        if not output_path:
            messagebox.showinfo("Info", "Analysis cancelled.")
            return

        try:
            # --- Data Loading ---
            polygons_gdf = gpd.read_file(polygon_path)
            points_gdf = gpd.read_file(point_path)

            # --- Area Calculation ---
            boundaries = polygons_gdf[polygons_gdf['HCV_catego'] == 0].copy()
            inclaves = polygons_gdf[polygons_gdf['HCV_catego'] == 1].copy()

            boundaries['LUAS_TOTAL'] = boundaries.geometry.area / 10000
            inclave_areas = inclaves.groupby(['Blok', 'Sub_Divisi'])['geometry'].apply(lambda g: g.unary_union.area / 10000).reset_index(name='LUAS_INCLAVE')

            result_gdf = boundaries.merge(inclave_areas, on=['Blok', 'Sub_Divisi'], how='left')
            result_gdf['LUAS_INCLAVE'] = result_gdf['LUAS_INCLAVE'].fillna(0)
            result_gdf['LUAS_NETTO'] = result_gdf['LUAS_TOTAL'] - result_gdf['LUAS_INCLAVE']

            # --- Tree Counting ---
            join_gdf = gpd.sjoin(points_gdf, result_gdf, how="inner", predicate='within')
            tree_counts = join_gdf.groupby(['Blok', 'Sub_Divisi']).size().reset_index(name='JML_POHON')

            final_gdf = result_gdf.merge(tree_counts, on=['Blok', 'Sub_Divisi'], how='left')
            final_gdf['JML_POHON'] = final_gdf['JML_POHON'].fillna(0).astype(int)

            # --- Save and Display Results ---
            output_columns = ['Blok', 'Sub_Divisi', 'JML_POHON', 'LUAS_TOTAL', 'LUAS_INCLAVE', 'LUAS_NETTO', 'geometry']
            final_gdf = final_gdf[output_columns]
            
            final_gdf.to_file(output_path, driver='ESRI Shapefile')

            # Clear and update the results table
            for i in self.tree.get_children():
                self.tree.delete(i)

            for index, row in final_gdf.iterrows():
                self.tree.insert("", "end", values=(
                    row['Blok'], row['Sub_Divisi'], row['JML_POHON'],
                    f"{row['LUAS_TOTAL']:.2f}", f"{row['LUAS_INCLAVE']:.2f}", f"{row['LUAS_NETTO']:.2f}"
                ))

            messagebox.showinfo("Success", f"Analysis complete. New shapefile saved to:\n{output_path}")

        except Exception as e:
            messagebox.showerror("Error", f"An error occurred: {e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = MapViewerGUI(root)
    root.mainloop()
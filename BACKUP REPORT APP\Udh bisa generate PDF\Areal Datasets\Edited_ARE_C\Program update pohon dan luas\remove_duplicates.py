import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon
import os

def remove_duplicates_from_shapefile(input_path, output_path=None, method='geometry'):
    """
    Menghapus duplikat dari shapefile polygon
    
    Parameters:
    - input_path: Path ke shapefile input
    - output_path: Path untuk menyimpan hasil (optional)
    - method: Metode deteksi duplikat ('geometry', 'attributes', 'both')
    
    Returns:
    - GeoDataFrame yang sudah dibersihkan dari duplikat
    """
    
    print("=== MENGHAPUS DUPLIKAT DARI SHAPEFILE ===")
    print(f"Input file: {input_path}")
    
    # Baca shapefile
    try:
        gdf = gpd.read_file(input_path)
        print(f"✓ Berhasil membaca {len(gdf)} features")
        print(f"Kolom yang tersedia: {list(gdf.columns)}")
    except Exception as e:
        print(f"Error membaca shapefile: {e}")
        return None
    
    # Simpan data asli untuk perbandingan
    original_count = len(gdf)
    
    print(f"\nMendeteksi duplikat dengan metode: {method}")
    
    if method == 'geometry':
        # Hapus duplikat berdasarkan geometri
        print("Menghapus duplikat berdasarkan geometri...")
        gdf_clean = gdf.drop_duplicates(subset=['geometry'])
        
    elif method == 'attributes':
        # Hapus duplikat berdasarkan atribut (kecuali geometry)
        print("Menghapus duplikat berdasarkan atribut...")
        attr_columns = [col for col in gdf.columns if col != 'geometry']
        gdf_clean = gdf.drop_duplicates(subset=attr_columns)
        
    elif method == 'both':
        # Hapus duplikat berdasarkan geometri DAN atribut
        print("Menghapus duplikat berdasarkan geometri dan atribut...")
        gdf_clean = gdf.drop_duplicates()
        
    else:
        print("Error: Metode tidak valid. Gunakan 'geometry', 'attributes', atau 'both'")
        return None
    
    # Reset index
    gdf_clean = gdf_clean.reset_index(drop=True)
    
    # Hitung hasil
    cleaned_count = len(gdf_clean)
    duplicates_removed = original_count - cleaned_count
    
    print(f"\n=== HASIL PEMBERSIHAN ===")
    print(f"Features asli: {original_count}")
    print(f"Features setelah dibersihkan: {cleaned_count}")
    print(f"Duplikat yang dihapus: {duplicates_removed}")
    
    if duplicates_removed > 0:
        print(f"✓ Berhasil menghapus {duplicates_removed} duplikat")
        
        # Tampilkan detail per BLOK jika ada
        if 'BLOK' in gdf_clean.columns:
            print(f"\nDetail per BLOK setelah pembersihan:")
            blok_counts = gdf_clean['BLOK'].value_counts().sort_index()
            for blok, count in blok_counts.items():
                print(f"   {blok}: {count} features")
                
            # Cek apakah masih ada duplikat BLOK
            duplicate_bloks = blok_counts[blok_counts > 1]
            if len(duplicate_bloks) > 0:
                print(f"\n⚠️  WARNING: Masih ada BLOK duplikat:")
                for blok, count in duplicate_bloks.items():
                    print(f"   {blok}: {count} features")
                    
                print("\nIni mungkin karena BLOK yang sama memiliki geometri berbeda.")
                print("Gunakan metode 'attributes' untuk menghapus berdasarkan BLOK yang sama.")
    else:
        print("✓ Tidak ada duplikat ditemukan")
    
    # Simpan hasil jika path output disediakan
    if output_path:
        try:
            gdf_clean.to_file(output_path)
            print(f"✓ Hasil disimpan ke: {output_path}")
        except Exception as e:
            print(f"Error menyimpan file: {e}")
    
    return gdf_clean

def advanced_duplicate_removal(input_path, output_path=None):
    """
    Pembersihan duplikat tingkat lanjut dengan berbagai opsi
    """
    
    print("=== ANALISIS DUPLIKAT TINGKAT LANJUT ===")
    
    # Baca shapefile
    try:
        gdf = gpd.read_file(input_path)
        print(f"✓ Loaded {len(gdf)} features")
    except Exception as e:
        print(f"Error: {e}")
        return None
    
    # Analisis berbagai jenis duplikat
    print(f"\n1. DUPLIKAT GEOMETRI:")
    geom_duplicates = gdf[gdf.geometry.duplicated(keep=False)]
    print(f"   Ditemukan {len(geom_duplicates)} features dengan geometri duplikat")
    
    if 'BLOK' in gdf.columns:
        print(f"\n2. DUPLIKAT BLOK:")
        blok_duplicates = gdf[gdf['BLOK'].duplicated(keep=False)]
        print(f"   Ditemukan {len(blok_duplicates)} features dengan BLOK duplikat")
        
        if len(blok_duplicates) > 0:
            print("   BLOK yang duplikat:")
            unique_bloks = blok_duplicates['BLOK'].unique()
            for blok in unique_bloks:
                if pd.isna(blok):
                    count = len(blok_duplicates[blok_duplicates['BLOK'].isna()])
                    print(f"      [Tidak ada BLOK]: {count} features")
                else:
                    count = len(blok_duplicates[blok_duplicates['BLOK'] == blok])
                    print(f"      {blok}: {count} features")
    
    # Analisis luas area
    if hasattr(gdf.geometry, 'area'):
        gdf['AREA_CALC'] = gdf.geometry.area
        print(f"\n3. DUPLIKAT BERDASARKAN LUAS:")
        area_duplicates = gdf[gdf['AREA_CALC'].duplicated(keep=False)]
        print(f"   Ditemukan {len(area_duplicates)} features dengan luas duplikat")
    
    # Menu pilihan pembersihan
    print(f"\n=== PILIHAN PEMBERSIHAN ===")
    print("1. Hapus duplikat geometri saja")
    print("2. Hapus duplikat BLOK saja")
    print("3. Hapus duplikat geometri DAN BLOK")
    print("4. Hapus duplikat berdasarkan semua atribut")
    print("5. Pembersihan otomatis (rekomendasi)")
    
    choice = input("\nPilih metode (1-5): ").strip()
    
    gdf_clean = gdf.copy()
    
    if choice == '1':
        gdf_clean = gdf.drop_duplicates(subset=['geometry'])
        method_used = "geometri"
        
    elif choice == '2' and 'BLOK' in gdf.columns:
        gdf_clean = gdf.drop_duplicates(subset=['BLOK'])
        method_used = "BLOK"
        
    elif choice == '3' and 'BLOK' in gdf.columns:
        gdf_clean = gdf.drop_duplicates(subset=['geometry', 'BLOK'])
        method_used = "geometri dan BLOK"
        
    elif choice == '4':
        # Hapus berdasarkan semua atribut kecuali geometry
        attr_cols = [col for col in gdf.columns if col != 'geometry']
        gdf_clean = gdf.drop_duplicates(subset=attr_cols)
        method_used = "semua atribut"
        
    elif choice == '5':
        # Pembersihan otomatis: prioritas pada geometri, lalu BLOK
        print("Melakukan pembersihan otomatis...")
        gdf_clean = gdf.drop_duplicates(subset=['geometry'])
        
        if 'BLOK' in gdf_clean.columns:
            # Jika masih ada BLOK duplikat, pilih yang pertama
            gdf_clean = gdf_clean.drop_duplicates(subset=['BLOK'])
        
        method_used = "otomatis (geometri + BLOK)"
        
    else:
        print("Pilihan tidak valid!")
        return None
    
    # Reset index
    gdf_clean = gdf_clean.reset_index(drop=True)
    
    # Hapus kolom sementara jika ada
    if 'AREA_CALC' in gdf_clean.columns:
        gdf_clean = gdf_clean.drop('AREA_CALC', axis=1)
    
    # Hasil
    original_count = len(gdf)
    cleaned_count = len(gdf_clean)
    removed_count = original_count - cleaned_count
    
    print(f"\n=== HASIL PEMBERSIHAN ({method_used}) ===")
    print(f"Features asli: {original_count}")
    print(f"Features bersih: {cleaned_count}")
    print(f"Duplikat dihapus: {removed_count}")
    
    if 'BLOK' in gdf_clean.columns:
        print(f"\nDaftar BLOK setelah pembersihan:")
        # Handle None values in BLOK column
        blok_values = gdf_clean['BLOK'].dropna().unique()
        blok_list = sorted([b for b in blok_values if b is not None])
        
        for blok in blok_list:
            count = len(gdf_clean[gdf_clean['BLOK'] == blok])
            print(f"   {blok}: {count} features")
            
        # Check for None values
        none_count = len(gdf_clean[gdf_clean['BLOK'].isna()])
        if none_count > 0:
            print(f"   [Tidak ada BLOK]: {none_count} features")
    
    # Simpan hasil
    if output_path:
        try:
            gdf_clean.to_file(output_path)
            print(f"✓ File bersih disimpan ke: {output_path}")
        except Exception as e:
            print(f"Error menyimpan: {e}")
    
    return gdf_clean

def main():
    """
    Fungsi utama untuk menjalankan pembersihan duplikat
    """
    
    # Path default
    default_input = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Polygon_Tambahan_ARE_C.shp"
    
    print("🧹 PEMBERSIHAN DUPLIKAT SHAPEFILE 🧹")
    print("="*50)
    
    # Pilih input file
    print("1. Gunakan file default")
    print("2. Input path manual")
    
    choice = input("\nPilihan (1-2): ").strip()
    
    if choice == '1':
        input_path = default_input
        if not os.path.exists(input_path):
            print(f"Error: File default tidak ditemukan: {input_path}")
            return
    elif choice == '2':
        input_path = input("Masukkan path shapefile: ").strip().strip('"')
        if not os.path.exists(input_path):
            print(f"Error: File tidak ditemukan: {input_path}")
            return
    else:
        print("Pilihan tidak valid!")
        return
    
    # Tentukan output path
    base_name = os.path.splitext(os.path.basename(input_path))[0]
    output_path = f"{base_name}_cleaned.shp"
    
    print(f"\nInput:  {input_path}")
    print(f"Output: {output_path}")
    
    # Jalankan pembersihan tingkat lanjut
    result = advanced_duplicate_removal(input_path, output_path)
    
    if result is not None:
        print(f"\n✅ PEMBERSIHAN SELESAI!")
        print(f"File bersih: {output_path}")
        print(f"Siap digunakan untuk analisis pohon.")
    else:
        print(f"\n❌ PEMBERSIHAN GAGAL!")

if __name__ == "__main__":
    main() 
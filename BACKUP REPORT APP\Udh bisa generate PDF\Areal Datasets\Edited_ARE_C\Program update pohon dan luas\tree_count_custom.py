import geopandas as gpd
import pandas as pd
import os
import sys
from pathlib import Path

def get_user_input():
    """
    Meminta input path dari user
    """
    print("=== TREE COUNTING ANALYSIS - CUSTOM PATHS ===")
    print()
    
    # Input path polygon
    print("1. Path ke shapefile POLYGON BLOK:")
    polygon_path = input("   Masukkan path lengkap: ").strip().strip('"')
    
    if not os.path.exists(polygon_path):
        print(f"   ERROR: File tidak ditemukan: {polygon_path}")
        return None, None, None
    
    # Input path points
    print("\n2. Path ke shapefile TITIK DETEKSI POHON:")
    points_path = input("   Masukkan path lengkap: ").strip().strip('"')
    
    if not os.path.exists(points_path):
        print(f"   ERROR: File tidak ditemukan: {points_path}")
        return None, None, None
    
    # Input nama output (optional)
    print("\n3. Nama file output (optional):")
    output_name = input("   Masukkan nama file (kosong = default): ").strip()
    
    if not output_name:
        output_name = "Polygon_dengan_Jumlah_Pohon.shp"
    elif not output_name.endswith('.shp'):
        output_name += '.shp'
    
    return polygon_path, points_path, output_name

def analyze_shapefiles_info(polygon_path, points_path):
    """
    Menganalisis informasi dasar dari shapefile
    """
    print("\n=== ANALISIS INFORMASI SHAPEFILE ===")
    
    try:
        # Baca polygon
        polygons = gpd.read_file(polygon_path)
        print(f"📍 POLYGON FILE:")
        print(f"   Records: {len(polygons)}")
        print(f"   CRS: {polygons.crs}")
        print(f"   Columns: {list(polygons.columns)}")
        
        # Cek apakah ada kolom BLOK atau yang mirip
        blok_columns = [col for col in polygons.columns if 'blok' in col.lower()]
        if blok_columns:
            print(f"   BLOK columns found: {blok_columns}")
        else:
            print("   WARNING: Tidak ditemukan kolom BLOK")
            print("   Available columns:", list(polygons.columns))
        
        # Baca points
        points = gpd.read_file(points_path)
        print(f"\n🌳 POINTS FILE:")
        print(f"   Records: {len(points)}")
        print(f"   CRS: {points.crs}")
        print(f"   Columns: {list(points.columns)}")
        
        # Check CRS compatibility
        if polygons.crs != points.crs:
            print(f"\n⚠️  WARNING: CRS berbeda!")
            print(f"   Polygon CRS: {polygons.crs}")
            print(f"   Points CRS: {points.crs}")
            print("   Script akan otomatis menyamakan CRS")
        else:
            print(f"\n✅ CRS sama: {polygons.crs}")
            
        return True
        
    except Exception as e:
        print(f"ERROR analyzing shapefiles: {e}")
        return False

def count_trees_advanced(polygon_path, points_path, output_path, blok_field='BLOK'):
    """
    Versi advanced dengan opsi custom field BLOK
    """
    print("\n=== MEMULAI ANALISIS SPATIAL ===")
    
    try:
        # Baca shapefiles
        print("Loading polygon data...")
        polygons = gpd.read_file(polygon_path)
        
        print("Loading points data...")
        points = gpd.read_file(points_path)
        
        # Auto-detect BLOK field jika tidak ada
        if blok_field not in polygons.columns:
            blok_candidates = [col for col in polygons.columns if 'blok' in col.lower()]
            if blok_candidates:
                blok_field = blok_candidates[0]
                print(f"Using BLOK field: {blok_field}")
            else:
                print("WARNING: No BLOK field found, using index as identifier")
                polygons['BLOK_AUTO'] = polygons.index + 1
                blok_field = 'BLOK_AUTO'
        
        # Samakan CRS
        if polygons.crs != points.crs:
            print(f"Converting CRS: {points.crs} -> {polygons.crs}")
            points = points.to_crs(polygons.crs)
        
        # Spatial join
        print("Performing spatial join...")
        joined = gpd.sjoin(points, polygons, how='inner', predicate='within')
        
        # Hitung per blok
        print("Counting trees per block...")
        tree_counts = joined.groupby(joined.index_right).size().reset_index(name='JUMLAH_POHON')
        tree_counts.rename(columns={'index_right': 'polygon_index'}, inplace=True)
        
        # Merge dengan polygon
        polygons_result = polygons.copy()
        polygons_result['polygon_index'] = polygons_result.index
        polygons_result = polygons_result.merge(tree_counts, on='polygon_index', how='left')
        polygons_result['JUMLAH_POHON'] = polygons_result['JUMLAH_POHON'].fillna(0).astype(int)
        polygons_result.drop('polygon_index', axis=1, inplace=True)
        
        # Tampilkan hasil
        print("\n=== HASIL PENGHITUNGAN ===")
        for idx, row in polygons_result.iterrows():
            blok_name = row[blok_field] if blok_field in row else f"Polygon_{idx}"
            print(f"   {blok_name}: {row['JUMLAH_POHON']} pohon")
        
        # Statistik
        total_trees = polygons_result['JUMLAH_POHON'].sum()
        avg_trees = polygons_result['JUMLAH_POHON'].mean()
        max_trees = polygons_result['JUMLAH_POHON'].max()
        min_trees = polygons_result['JUMLAH_POHON'].min()
        
        print(f"\n=== STATISTIK ===")
        print(f"   Total blok: {len(polygons_result)}")
        print(f"   Total pohon: {total_trees}")
        print(f"   Rata-rata: {avg_trees:.2f} pohon/blok")
        print(f"   Maximum: {max_trees} pohon")
        print(f"   Minimum: {min_trees} pohon")
        
        # Simpan hasil
        print(f"\nSaving to: {output_path}")
        polygons_result.to_file(output_path)
        print("✅ File berhasil disimpan!")
        
        return polygons_result
        
    except Exception as e:
        print(f"ERROR during analysis: {e}")
        return None

def main():
    """
    Main function dengan menu interaktif
    """
    print("🌳 TREE COUNTING ANALYSIS - CUSTOM VERSION 🌳")
    
    while True:
        print("\n" + "="*50)
        print("PILIH MODE:")
        print("1. Mode Otomatis (gunakan path default)")
        print("2. Mode Custom (input path manual)")
        print("3. Keluar")
        
        choice = input("\nPilihan (1-3): ").strip()
        
        if choice == '1':
            # Mode otomatis
            polygon_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Polygon_Tambahan_ARE_C.shp"
            points_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"
            output_path = "Polygon_ARE_C_dengan_Jumlah_Pohon_Auto.shp"
            
            if os.path.exists(polygon_path) and os.path.exists(points_path):
                analyze_shapefiles_info(polygon_path, points_path)
                result = count_trees_advanced(polygon_path, points_path, output_path)
            else:
                print("ERROR: File default tidak ditemukan!")
                
        elif choice == '2':
            # Mode custom
            polygon_path, points_path, output_path = get_user_input()
            
            if polygon_path and points_path:
                analyze_shapefiles_info(polygon_path, points_path)
                
                # Konfirmasi
                print(f"\n=== KONFIRMASI ===")
                print(f"Polygon: {polygon_path}")
                print(f"Points:  {points_path}")
                print(f"Output:  {output_path}")
                
                confirm = input("\nLanjutkan analisis? (y/n): ").strip().lower()
                if confirm in ['y', 'yes', 'ya']:
                    result = count_trees_advanced(polygon_path, points_path, output_path)
                else:
                    print("Analisis dibatalkan.")
                    
        elif choice == '3':
            print("Terima kasih!")
            break
            
        else:
            print("Pilihan tidak valid!")

if __name__ == "__main__":
    main() 
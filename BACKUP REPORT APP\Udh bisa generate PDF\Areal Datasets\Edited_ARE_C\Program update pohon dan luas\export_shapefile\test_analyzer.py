"""
Test script for ARE C Advanced Spatial Analyzer
Verifies that all dependencies are available and basic functionality works
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✓ tkinter available")
    except ImportError as e:
        print(f"✗ tkinter not available: {e}")
        return False
    
    try:
        import geopandas as gpd
        print(f"✓ geopandas {gpd.__version__} available")
    except ImportError as e:
        print(f"✗ geopandas not available: {e}")
        return False
    
    try:
        import pandas as pd
        print(f"✓ pandas {pd.__version__} available")
    except ImportError as e:
        print(f"✗ pandas not available: {e}")
        return False
    
    try:
        import shapely
        print(f"✓ shapely {shapely.__version__} available")
    except ImportError as e:
        print(f"✗ shapely not available: {e}")
        return False
    
    try:
        import openpyxl
        print(f"✓ openpyxl available")
    except ImportError as e:
        print(f"✗ openpyxl not available: {e}")
        return False
    
    return True

def test_file_paths():
    """Test if required data files exist"""
    print("\nTesting file paths...")
    
    boundary_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
    points_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"
    
    if os.path.exists(boundary_path):
        print(f"✓ Boundary shapefile found: {boundary_path}")
    else:
        print(f"✗ Boundary shapefile not found: {boundary_path}")
        return False
    
    if os.path.exists(points_path):
        print(f"✓ Detection points shapefile found: {points_path}")
    else:
        print(f"✗ Detection points shapefile not found: {points_path}")
        return False
    
    return True

def test_shapefile_loading():
    """Test if shapefiles can be loaded"""
    print("\nTesting shapefile loading...")
    
    try:
        import geopandas as gpd
        
        boundary_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
        points_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"
        
        if os.path.exists(boundary_path):
            gdf_boundary = gpd.read_file(boundary_path)
            print(f"✓ Boundary shapefile loaded: {len(gdf_boundary)} features")
            print(f"  Columns: {list(gdf_boundary.columns)}")
            print(f"  CRS: {gdf_boundary.crs}")
        
        if os.path.exists(points_path):
            gdf_points = gpd.read_file(points_path)
            print(f"✓ Points shapefile loaded: {len(gdf_points)} features")
            print(f"  Columns: {list(gdf_points.columns)}")
            print(f"  CRS: {gdf_points.crs}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error loading shapefiles: {e}")
        return False

def test_gui_creation():
    """Test if GUI can be created (without showing it)"""
    print("\nTesting GUI creation...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # Create a test window
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Test basic widgets
        frame = ttk.Frame(root)
        label = ttk.Label(frame, text="Test")
        button = ttk.Button(frame, text="Test")
        
        print("✓ GUI components can be created")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ Error creating GUI: {e}")
        return False

def main():
    """Run all tests"""
    print("ARE C Advanced Spatial Analyzer - System Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("File Path Test", test_file_paths),
        ("Shapefile Loading Test", test_shapefile_loading),
        ("GUI Creation Test", test_gui_creation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n✓ All tests passed! The application should work correctly.")
        print("You can now run: python are_c_advanced_spatial_analyzer.py")
    else:
        print(f"\n✗ {total - passed} test(s) failed. Please resolve issues before running the application.")
        
        if not any(result for _, result in results[:2]):  # Import or file path tests failed
            print("\nSuggested actions:")
            print("1. Install missing dependencies: pip install -r requirements_advanced_analyzer.txt")
            print("2. Verify that the shapefile paths are correct")
            print("3. Ensure Python 3.8+ is installed")

if __name__ == "__main__":
    main()

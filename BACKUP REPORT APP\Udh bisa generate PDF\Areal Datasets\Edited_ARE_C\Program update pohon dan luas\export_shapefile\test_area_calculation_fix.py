"""
Test script untuk memverifikasi perbaikan perhitungan luas polygon
Script ini menggunakan metode yang sama dengan tree_count_overlay_accurate.py
"""

import geopandas as gpd
import pandas as pd
import os

def test_correct_area_calculation():
    """Test area calculation dengan metode yang benar"""
    print("TESTING CORRECT AREA CALCULATION METHOD")
    print("=" * 50)
    
    boundary_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
    
    if not os.path.exists(boundary_path):
        print(f"❌ File not found: {boundary_path}")
        return False
    
    try:
        # Load data
        gdf = gpd.read_file(boundary_path)
        print(f"✅ Loaded {len(gdf)} polygons")
        print(f"Original CRS: {gdf.crs}")
        
        # Standardize column names
        boundary_mappings = {
            'SUB_DIVISI': 'SUBDIVISI',
            'HCV_Catego': 'HCV',
            'Jumlah_Poh': 'JUMLAH_POH'
        }
        
        for old_name, new_name in boundary_mappings.items():
            if old_name in gdf.columns and new_name not in gdf.columns:
                gdf = gdf.rename(columns={old_name: new_name})
        
        # Clean data types
        numeric_columns = ['JUMLAH_POH', 'LUAS_AUTO']
        for col in numeric_columns:
            if col in gdf.columns:
                gdf[col] = pd.to_numeric(gdf[col], errors='coerce').fillna(0)
        
        gdf['HCV'] = gdf['HCV'].astype(str)
        
        # STEP 1: Check if CRS is projected
        print(f"\n=== CRS ANALYSIS ===")
        print(f"Current CRS: {gdf.crs}")
        print(f"Is projected: {gdf.crs.is_projected if gdf.crs else False}")
        
        # STEP 2: Convert to projected CRS if needed (following tree_count_overlay_accurate.py method)
        if not gdf.crs or not gdf.crs.is_projected:
            print("Converting to projected CRS (UTM Zone 48S - EPSG:32748)...")
            gdf = gdf.to_crs(epsg=32748)
            print(f"New CRS: {gdf.crs}")
        else:
            print("CRS already projected, ready for area calculation")
        
        # STEP 3: Calculate area from geometry (the correct way)
        print(f"\n=== AREA CALCULATION ===")
        gdf['LUAS_HA_CALCULATED'] = gdf.geometry.area / 10000  # m² to hectares
        
        total_area_calculated = gdf['LUAS_HA_CALCULATED'].sum()
        print(f"Total calculated area: {total_area_calculated:.2f} hectares")
        
        # Compare with existing LUAS_AUTO if available
        if 'LUAS_AUTO' in gdf.columns:
            total_luas_auto = gdf['LUAS_AUTO'].sum()
            print(f"Total LUAS_AUTO: {total_luas_auto:.2f}")
            print(f"Difference: {abs(total_area_calculated - total_luas_auto):.2f}")
            
            if total_luas_auto > total_area_calculated * 100:
                print("LUAS_AUTO appears to be in square meters")
                gdf['LUAS_AUTO_HA'] = gdf['LUAS_AUTO'] / 10000
                total_luas_auto_ha = gdf['LUAS_AUTO_HA'].sum()
                print(f"LUAS_AUTO converted to hectares: {total_luas_auto_ha:.2f}")
        
        # STEP 4: Test area calculation by Block/Sub Division
        print(f"\n=== AREA BY BLOCK/SUBDIVISION ===")
        grouped = gdf.groupby(['BLOK', 'SUBDIVISI'])
        
        print(f"Number of Block/Sub Division combinations: {len(grouped)}")
        
        results = []
        for (blok, subdivisi), group in grouped:
            # Calculate area for this group
            group_area = group['LUAS_HA_CALCULATED'].sum()
            tree_count = group['JUMLAH_POH'].sum() if 'JUMLAH_POH' in group.columns else 0
            polygon_count = len(group)
            
            results.append({
                'BLOK': blok,
                'SUBDIVISI': subdivisi,
                'area_ha': group_area,
                'tree_count': tree_count,
                'polygon_count': polygon_count
            })
            
            print(f"  {blok}-{subdivisi}: {group_area:.2f}ha, {tree_count} trees, {polygon_count} polygons")
        
        # STEP 5: Summary statistics
        print(f"\n=== SUMMARY STATISTICS ===")
        results_df = pd.DataFrame(results)
        
        total_area = results_df['area_ha'].sum()
        total_trees = results_df['tree_count'].sum()
        total_polygons = results_df['polygon_count'].sum()
        
        print(f"Total area: {total_area:.2f} hectares")
        print(f"Total trees: {total_trees:,}")
        print(f"Total polygons: {total_polygons}")
        print(f"Average area per polygon: {total_area/total_polygons:.2f} ha")
        print(f"Average trees per hectare: {total_trees/total_area:.1f} trees/ha")
        
        # STEP 6: Check for zero areas
        zero_area_groups = results_df[results_df['area_ha'] == 0]
        if len(zero_area_groups) > 0:
            print(f"\n⚠️  WARNING: {len(zero_area_groups)} groups with zero area:")
            for _, row in zero_area_groups.iterrows():
                print(f"  {row['BLOK']}-{row['SUBDIVISI']}: {row['polygon_count']} polygons")
        else:
            print(f"\n✅ All groups have non-zero areas")
        
        # STEP 7: Sample detailed analysis
        print(f"\n=== SAMPLE POLYGON DETAILS ===")
        sample_polygons = gdf.head(5)
        for idx, row in sample_polygons.iterrows():
            area_m2 = row.geometry.area
            area_ha = area_m2 / 10000
            print(f"Polygon {idx}:")
            print(f"  BLOK: {row['BLOK']}, SUBDIVISI: {row['SUBDIVISI']}")
            print(f"  Area (m²): {area_m2:,.0f}")
            print(f"  Area (ha): {area_ha:.2f}")
            if 'LUAS_AUTO' in row:
                print(f"  LUAS_AUTO: {row['LUAS_AUTO']}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run area calculation test"""
    print("Area Calculation Fix Verification")
    print("=" * 50)
    print("This test uses the same method as tree_count_overlay_accurate.py")
    print("to ensure correct area calculation for LSU polygons.")
    print()
    
    result = test_correct_area_calculation()
    
    if result:
        print(f"\n" + "=" * 50)
        print("✅ AREA CALCULATION TEST COMPLETED")
        print("=" * 50)
        print("Key findings:")
        print("1. CRS conversion to projected coordinate system")
        print("2. Area calculation from geometry.area / 10000")
        print("3. Verification of non-zero areas for all groups")
        print("4. Comparison with existing LUAS_AUTO values")
        print()
        print("The advanced analyzer should now calculate areas correctly!")
    else:
        print(f"\n❌ AREA CALCULATION TEST FAILED")
        print("Please check the error messages above.")

if __name__ == "__main__":
    main()

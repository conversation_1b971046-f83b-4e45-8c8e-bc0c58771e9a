import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import geopandas as gpd
import pandas as pd
import os

class BlokPohonApp:
    def __init__(self, root):
        self.root = root
        self.root.title('<PERSON><PERSON><PERSON> & <PERSON><PERSON> (ARE C)')
        self.root.geometry('1100x600')
        
        self.boundary_path = tk.StringVar()
        self.titik_path = tk.StringVar()
        self.df_summary = None
        self.gdf_boundary = None
        
        self.create_widgets()

    def create_widgets(self):
        frm = tk.Frame(self.root)
        frm.pack(padx=10, pady=10, fill='x')
        
        tk.Label(frm, text='Shapefile Boundary (ARE C):').grid(row=0, column=0, sticky='w')
        tk.Entry(frm, textvariable=self.boundary_path, width=80).grid(row=0, column=1)
        tk.Button(frm, text='Browse', command=self.browse_boundary).grid(row=0, column=2)
        
        tk.Label(frm, text='Shapefile Titik Pohon:').grid(row=1, column=0, sticky='w')
        tk.Entry(frm, textvariable=self.titik_path, width=80).grid(row=1, column=1)
        tk.Button(frm, text='Browse', command=self.browse_titik).grid(row=1, column=2)
        
        tk.Button(frm, text='Proses & Tampilkan', command=self.proses).grid(row=2, column=1, pady=10)
        tk.Button(frm, text='Simpan ke Excel/CSV', command=self.simpan, state='disabled').grid(row=2, column=2, pady=10)
        
        self.tree = ttk.Treeview(self.root, columns=("BLOK", "SUBDIVISI", "luas_total", "luas_inclave", "luas_netto", "jumlah_pohon"), show='headings')
        for col in ("BLOK", "SUBDIVISI", "luas_total", "luas_inclave", "luas_netto", "jumlah_pohon"):
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120)
        self.tree.pack(fill='both', expand=True, padx=10, pady=10)

    def browse_boundary(self):
        path = filedialog.askopenfilename(filetypes=[('Shapefile', '*.shp')])
        if path:
            self.boundary_path.set(path)

    def browse_titik(self):
        path = filedialog.askopenfilename(filetypes=[('Shapefile', '*.shp')])
        if path:
            self.titik_path.set(path)

    def proses(self):
        boundary_path = self.boundary_path.get()
        titik_path = self.titik_path.get()
        if not boundary_path or not titik_path:
            messagebox.showerror('Error', 'Pilih file boundary dan titik pohon!')
            return
        try:
            gdf_boundary = gpd.read_file(boundary_path)
            gdf_titik = gpd.read_file(titik_path)
        except Exception as e:
            messagebox.showerror('Error', f'Gagal membaca shapefile: {e}')
            return
        # Pastikan kolom yang diperlukan ada
        for col in ['BLOK', 'SUBDIVISI', 'HCV', 'geometry']:
            if col not in gdf_boundary.columns:
                messagebox.showerror('Error', f"Kolom '{col}' tidak ditemukan di boundary!")
                return
        # Pastikan kolom luas ada
        if 'luas_auto' not in gdf_boundary.columns:
            gdf_boundary['luas_auto'] = gdf_boundary.geometry.area
        # Pastikan kolom HCV di boundary bertipe str/int
        gdf_boundary['HCV'] = gdf_boundary['HCV'].astype(str)
        # 1. Hitung luas boundary (HCV==0) dan luas inclave (HCV==1) per BLOK & SUBDIVISI
        df_boundary = gdf_boundary.copy()
        df_boundary['luas_auto'] = pd.to_numeric(df_boundary['luas_auto'], errors='coerce')
        # Luas total (HCV 0)
        luas_total = df_boundary[df_boundary['HCV'].isin(['0', 0])].groupby(['BLOK', 'SUBDIVISI'])['luas_auto'].sum().reset_index().rename(columns={'luas_auto':'luas_total'})
        # Luas inclave (HCV 1)
        luas_inclave = df_boundary[df_boundary['HCV'].isin(['1', 1])].groupby(['BLOK', 'SUBDIVISI'])['luas_auto'].sum().reset_index().rename(columns={'luas_auto':'luas_inclave'})
        # Gabung
        df_sum = pd.merge(luas_total, luas_inclave, on=['BLOK','SUBDIVISI'], how='left').fillna({'luas_inclave':0})
        df_sum['luas_netto'] = df_sum['luas_total'] - df_sum['luas_inclave']
        # 2. Overlay titik pohon ke boundary (HCV 0 saja)
        gdf_boundary0 = gdf_boundary[gdf_boundary['HCV'].isin(['0', 0])].copy()
        gdf_titik = gdf_titik.to_crs(gdf_boundary0.crs)
        join = gpd.sjoin(gdf_titik, gdf_boundary0, how='left', predicate='within')
        pohon_count = join.groupby(['BLOK','SUBDIVISI']).size().reset_index(name='jumlah_pohon')
        # Gabung ke summary
        df_sum = pd.merge(df_sum, pohon_count, on=['BLOK','SUBDIVISI'], how='left').fillna({'jumlah_pohon':0})
        # Update kolom luas_total di boundary (HCV 0)
        for idx, row in df_sum.iterrows():
            mask = (gdf_boundary['BLOK']==row['BLOK']) & (gdf_boundary['SUBDIVISI']==row['SUBDIVISI']) & (gdf_boundary['HCV'].isin(['0', 0]))
            gdf_boundary.loc[mask, 'luas_total'] = row['luas_total']
            gdf_boundary.loc[mask, 'luas_inclave'] = row['luas_inclave']
            gdf_boundary.loc[mask, 'luas_netto'] = row['luas_netto']
            gdf_boundary.loc[mask, 'jumlah_pohon'] = row['jumlah_pohon']
        self.gdf_boundary = gdf_boundary
        self.df_summary = df_sum
        # Tampilkan di tabel
        for i in self.tree.get_children():
            self.tree.delete(i)
        for _, row in df_sum.iterrows():
            self.tree.insert('', 'end', values=(row['BLOK'], row['SUBDIVISI'], f"{row['luas_total']:.2f}", f"{row['luas_inclave']:.2f}", f"{row['luas_netto']:.2f}", int(row['jumlah_pohon'])))
        # Enable tombol simpan
        for child in self.root.winfo_children():
            for btn in child.winfo_children():
                if isinstance(btn, tk.Button) and btn['text'].startswith('Simpan'):
                    btn['state'] = 'normal'

    def simpan(self):
        if self.df_summary is None:
            messagebox.showerror('Error', 'Belum ada data untuk disimpan!')
            return
        save_path = filedialog.asksaveasfilename(defaultextension='.xlsx', filetypes=[('Excel','*.xlsx'),('CSV','*.csv')])
        if not save_path:
            return
        try:
            if save_path.endswith('.xlsx'):
                with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                    self.df_summary.to_excel(writer, index=False, sheet_name='Summary')
            else:
                self.df_summary.to_csv(save_path, index=False)
            # Simpan boundary yang sudah diupdate
            if self.gdf_boundary is not None:
                shp_out = os.path.splitext(save_path)[0] + '_boundary_update.shp'
                self.gdf_boundary.to_file(shp_out)
            messagebox.showinfo('Sukses', f'Hasil dan boundary terupdate disimpan ke:\n{save_path}')
        except Exception as e:
            messagebox.showerror('Error', f'Gagal menyimpan: {e}')

def main():
    root = tk.Tk()
    app = BlokPohonApp(root)
    root.mainloop()

if __name__ == '__main__':
    main() 
"""
Script Analisis Data Boundary Blok ARE C
Script ini akan menganalisis struktur data shapefile boundary untuk memahami:
1. <PERSON><PERSON>a nomor blok yang tersedia
2. Struktur kolom dan data
3. Distribusi HCV kategori
4. Luas per blok
5. Informasi geometry

Author: Generated for ARE C Analysis
Date: 2025-01-03
"""

import geopandas as gpd
import pandas as pd
import os

def analyze_boundary_shapefile():
    """Analisis komprehensif shapefile boundary"""
    
    print("=" * 70)
    print("ANALISIS DATA BOUNDARY BLOK ARE C")
    print("=" * 70)
    
    boundary_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
    
    if not os.path.exists(boundary_path):
        print(f"❌ File tidak ditemukan: {boundary_path}")
        return None
    
    try:
        # Load shapefile
        print("📂 Loading shapefile...")
        gdf = gpd.read_file(boundary_path)
        print(f"✅ Berhasil memuat {len(gdf)} fitur/polygon")
        print(f"📍 CRS: {gdf.crs}")
        
        # Standardisasi nama kolom
        print("\n📋 INFORMASI KOLOM:")
        print("-" * 50)
        print("Kolom yang tersedia:")
        for i, col in enumerate(gdf.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # Standardisasi nama kolom jika diperlukan
        column_mappings = {
            'SUB_DIVISI': 'SUBDIVISI',
            'HCV_Catego': 'HCV',
            'Jumlah_Poh': 'JUMLAH_POH'
        }
        
        for old_name, new_name in column_mappings.items():
            if old_name in gdf.columns and new_name not in gdf.columns:
                gdf = gdf.rename(columns={old_name: new_name})
                print(f"🔄 Mengganti nama kolom: {old_name} → {new_name}")
        
        print(f"\nKolom setelah standardisasi: {list(gdf.columns)}")
        
        # Analisis kolom BLOK
        print("\n🏢 ANALISIS BLOK:")
        print("-" * 50)
        
        if 'BLOK' in gdf.columns:
            unique_bloks = sorted(gdf['BLOK'].unique())
            print(f"Total nomor blok unik: {len(unique_bloks)}")
            print("Daftar semua blok:")
            
            # Tampilkan blok dalam format kolom
            for i, blok in enumerate(unique_bloks):
                if i % 5 == 0:
                    print()
                print(f"  {blok:<15}", end="")
            print("\n")
            
            # Hitung fitur per blok
            blok_counts = gdf['BLOK'].value_counts().sort_index()
            print("Jumlah fitur per blok:")
            for blok, count in blok_counts.items():
                print(f"  {blok}: {count} fitur")
        
        else:
            print("❌ Kolom 'BLOK' tidak ditemukan!")
        
        # Analisis kolom SUBDIVISI
        print("\n📍 ANALISIS SUB DIVISI:")
        print("-" * 50)
        
        if 'SUBDIVISI' in gdf.columns:
            unique_subdivs = sorted(gdf['SUBDIVISI'].unique())
            print(f"Total sub divisi unik: {len(unique_subdivs)}")
            print("Daftar sub divisi:")
            for subdiv in unique_subdivs:
                print(f"  {subdiv}")
        else:
            print("❌ Kolom 'SUBDIVISI' tidak ditemukan!")
        
        # Analisis HCV kategori
        print("\n🏷️ ANALISIS HCV KATEGORI:")
        print("-" * 50)
        
        if 'HCV' in gdf.columns:
            hcv_counts = gdf['HCV'].value_counts()
            print("Distribusi HCV kategori:")
            for hcv, count in hcv_counts.items():
                print(f"  {hcv}: {count} fitur")
                
            # Analisis pola HCV
            print("\nPola HCV unik:")
            unique_hcv = gdf['HCV'].unique()
            for hcv in unique_hcv:
                print(f"  '{hcv}'")
        else:
            print("❌ Kolom 'HCV' tidak ditemukan!")
        
        # Analisis kombinasi BLOK + SUBDIVISI + HCV
        if all(col in gdf.columns for col in ['BLOK', 'SUBDIVISI', 'HCV']):
            print("\n🔍 ANALISIS KOMBINASI BLOK-SUBDIVISI-HCV:")
            print("-" * 50)
            
            combination_counts = gdf.groupby(['BLOK', 'SUBDIVISI', 'HCV']).size().reset_index(name='count')
            
            print("Kombinasi Blok-SubDivisi-HCV (sample 20 pertama):")
            print(combination_counts.head(20).to_string(index=False))
            
            # Hitung boundary vs enclave per blok
            print("\nAnalisis Boundary vs Enclave per Blok:")
            for blok in sorted(gdf['BLOK'].unique()):
                blok_data = gdf[gdf['BLOK'] == blok]
                
                boundary_count = len(blok_data[blok_data['HCV'].str.contains('Boundary', na=False)])
                enclave_count = len(blok_data[blok_data['HCV'].str.contains('nclave', na=False)])
                other_count = len(blok_data) - boundary_count - enclave_count
                
                if boundary_count > 0 or enclave_count > 0:
                    print(f"  {blok}: {boundary_count} boundary, {enclave_count} enclave, {other_count} lainnya")
        
        # Analisis area
        print("\n📏 ANALISIS LUAS:")
        print("-" * 50)
        
        # Hitung luas dari geometry (dalam proyeksi yang benar)
        if not gdf.crs or not gdf.crs.is_projected:
            print("🔄 Mengkonversi ke proyeksi UTM untuk perhitungan luas...")
            gdf_projected = gdf.to_crs(epsg=32748)  # UTM Zone 48S
        else:
            gdf_projected = gdf
        
        gdf_projected['geom_area_sqm'] = gdf_projected.geometry.area
        gdf_projected['geom_area_ha'] = gdf_projected['geom_area_sqm'] / 10000
        
        print("Statistik luas dari geometry:")
        area_stats = gdf_projected['geom_area_ha'].describe()
        print(f"  Total luas: {gdf_projected['geom_area_ha'].sum():,.2f} ha")
        print(f"  Rata-rata: {area_stats['mean']:,.2f} ha")
        print(f"  Minimum: {area_stats['min']:,.2f} ha")
        print(f"  Maksimum: {area_stats['max']:,.2f} ha")
        
        # Analisis luas per blok
        if 'BLOK' in gdf_projected.columns:
            print("\nLuas total per blok:")
            blok_areas = gdf_projected.groupby('BLOK')['geom_area_ha'].sum().sort_values(ascending=False)
            for blok, area in blok_areas.items():
                print(f"  {blok}: {area:,.2f} ha")
        
        # Cek kolom luas yang sudah ada
        area_columns = ['LUAS_AUTO', 'luas_aut_1', 'total_incl', 'luas_netto', 'luas_asss']
        existing_area_cols = [col for col in area_columns if col in gdf.columns]
        
        if existing_area_cols:
            print(f"\nKolom luas yang sudah ada: {existing_area_cols}")
            for col in existing_area_cols:
                if gdf[col].dtype in ['int64', 'float64']:
                    total = gdf[col].sum()
                    print(f"  {col}: Total = {total:,.2f}")
        
        # Analisis data pohon
        print("\n🌳 ANALISIS DATA POHON:")
        print("-" * 50)
        
        tree_columns = ['JUMLAH_POH', 'Jumlah_Poh']
        existing_tree_cols = [col for col in tree_columns if col in gdf.columns]
        
        if existing_tree_cols:
            for col in existing_tree_cols:
                # Konversi ke numerik dengan aman
                tree_values = pd.to_numeric(gdf[col], errors='coerce').fillna(0)
                total_trees = tree_values.sum()
                non_zero_count = (tree_values > 0).sum()
                
                print(f"Kolom {col}:")
                print(f"  Total pohon: {total_trees:,.0f}")
                print(f"  Fitur dengan pohon > 0: {non_zero_count}/{len(gdf)}")
                
                if 'BLOK' in gdf.columns:
                    print(f"  Pohon per blok:")
                    gdf_temp = gdf.copy()
                    gdf_temp[col] = tree_values
                    blok_trees = gdf_temp.groupby('BLOK')[col].sum().sort_values(ascending=False)
                    for blok, trees in blok_trees.head(10).items():
                        if trees > 0:
                            print(f"    {blok}: {trees:,.0f} pohon")
        else:
            print("❌ Tidak ada kolom data pohon yang ditemukan!")
        
        # Sample data
        print("\n📋 SAMPLE DATA (5 record pertama):")
        print("-" * 50)
        
        display_columns = ['BLOK', 'SUBDIVISI', 'HCV']
        if 'LUAS_AUTO' in gdf.columns:
            display_columns.append('LUAS_AUTO')
        if 'JUMLAH_POH' in gdf.columns:
            display_columns.append('JUMLAH_POH')
        
        available_columns = [col for col in display_columns if col in gdf.columns]
        
        if available_columns:
            print(gdf[available_columns].head().to_string(index=False))
        
        print("\n" + "=" * 70)
        print("ANALISIS SELESAI")
        print("=" * 70)
        
        return gdf
        
    except Exception as e:
        print(f"❌ Error saat menganalisis data: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    analyze_boundary_shapefile() 
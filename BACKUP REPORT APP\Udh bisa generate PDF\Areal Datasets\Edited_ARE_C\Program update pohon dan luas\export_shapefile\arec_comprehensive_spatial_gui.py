"""
ARE C Comprehensive Spatial Analysis GUI
Advanced palm tree detection analysis with complete spatial processing capabilities.

Features:
- Comprehensive attribute table display
- Spatial overlay analysis (point-in-polygon)
- HCV category processing (boundary vs enclave)
- Advanced polygon hole creation
- Area calculations and updates
- Tree count overlay analysis
- Export to multiple formats
- Real-time logging and progress tracking

Author: Generated for ARE C Palm Tree Analysis
Date: 2025-01-03
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import geopandas as gpd
import pandas as pd
import os
import logging
from datetime import datetime
from shapely.geometry import Polygon, MultiPolygon
from shapely.ops import unary_union
import threading
import traceback

# Default file paths
DEFAULT_BOUNDARY_PATH = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
DEFAULT_POINTS_PATH = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"

class ARECComprehensiveSpatialGUI:
    def __init__(self, root):
        self.root = root
        self.root.title('ARE C Comprehensive Spatial Analysis - Palm Tree Detection')
        self.root.geometry('1600x1000')
        self.root.minsize(1400, 900)
        
        # Data paths
        self.boundary_path = DEFAULT_BOUNDARY_PATH
        self.points_path = DEFAULT_POINTS_PATH
        
        # Data storage
        self.gdf_boundary = None
        self.gdf_points = None
        self.processed_gdf = None
        self.analysis_summary = None
        
        # Processing controls
        self.processing_active = False
        self.cancel_processing = False
        
        # Setup logging
        self.setup_logging()
        
        # Create GUI
        self.create_gui()
        
        # Load initial data
        self.load_initial_data()

    def setup_logging(self):
        """Setup logging system"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = f"arec_comprehensive_analysis_{timestamp}.log"
        
        logging.basicConfig(
            filename=self.log_file,
            level=logging.INFO,
            format='%(asctime)s %(levelname)s: %(message)s'
        )
        
        self.logger = logging.getLogger(__name__)

    def log_message(self, message, level='INFO'):
        """Log message to both file and GUI"""
        self.log_text.configure(state='normal')
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert('end', f"[{timestamp}] {message}\n")
        self.log_text.see('end')
        self.log_text.configure(state='disabled')
        
        if level == 'INFO':
            self.logger.info(message)
        elif level == 'ERROR':
            self.logger.error(message)
        elif level == 'WARNING':
            self.logger.warning(message)

    def create_gui(self):
        """Create the main GUI interface"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Create tabs
        self.create_data_tab()
        self.create_analysis_tab()
        self.create_results_tab()
        self.create_log_tab()

    def create_data_tab(self):
        """Create data management tab"""
        self.data_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_frame, text='Data Management')
        
        # File path section
        path_frame = ttk.LabelFrame(self.data_frame, text='Data Sources', padding=10)
        path_frame.pack(fill='x', padx=10, pady=5)
        
        # Boundary shapefile
        ttk.Label(path_frame, text='Boundary Polygon Shapefile:').grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.boundary_entry = ttk.Entry(path_frame, width=100)
        self.boundary_entry.grid(row=0, column=1, padx=5, pady=2)
        self.boundary_entry.insert(0, self.boundary_path)
        ttk.Button(path_frame, text='Browse', command=self.browse_boundary).grid(row=0, column=2, padx=5, pady=2)
        
        # Detection points shapefile
        ttk.Label(path_frame, text='Detection Points Shapefile:').grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.points_entry = ttk.Entry(path_frame, width=100)
        self.points_entry.grid(row=1, column=1, padx=5, pady=2)
        self.points_entry.insert(0, self.points_path)
        ttk.Button(path_frame, text='Browse', command=self.browse_points).grid(row=1, column=2, padx=5, pady=2)
        
        # Control buttons
        control_frame = ttk.Frame(path_frame)
        control_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        ttk.Button(control_frame, text='Load Data', command=self.load_initial_data).pack(side='left', padx=5)
        ttk.Button(control_frame, text='Validate Data', command=self.validate_data).pack(side='left', padx=5)
        
        # Data preview section
        preview_frame = ttk.LabelFrame(self.data_frame, text='Data Preview', padding=10)
        preview_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Boundary data preview
        bound_frame = ttk.LabelFrame(preview_frame, text='Boundary Data Preview')
        bound_frame.pack(fill='both', expand=True, pady=5)
        
        # Create treeview for boundary preview
        bound_columns = ('SUB_DIVISI', 'BLOK', 'LUAS_AUTO', 'Jumlah_Poh', 'HCV_Catego', 'ID_Feature')
        self.boundary_tree = ttk.Treeview(bound_frame, columns=bound_columns, show='headings', height=8)
        
        for col in bound_columns:
            self.boundary_tree.heading(col, text=col)
            self.boundary_tree.column(col, width=120)
        
        # Scrollbars for boundary preview
        bound_v_scroll = ttk.Scrollbar(bound_frame, orient='vertical', command=self.boundary_tree.yview)
        bound_h_scroll = ttk.Scrollbar(bound_frame, orient='horizontal', command=self.boundary_tree.xview)
        self.boundary_tree.configure(yscrollcommand=bound_v_scroll.set, xscrollcommand=bound_h_scroll.set)
        
        self.boundary_tree.pack(side='left', fill='both', expand=True)
        bound_v_scroll.pack(side='right', fill='y')

    def create_analysis_tab(self):
        """Create analysis configuration tab"""
        self.analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analysis_frame, text='Spatial Analysis')
        
        # Analysis options
        options_frame = ttk.LabelFrame(self.analysis_frame, text='Analysis Options', padding=10)
        options_frame.pack(fill='x', padx=10, pady=5)
        
        # Analysis checkboxes
        self.spatial_overlay_var = tk.BooleanVar(value=True)
        self.hcv_processing_var = tk.BooleanVar(value=True)
        self.area_calculation_var = tk.BooleanVar(value=True)
        self.hole_creation_var = tk.BooleanVar(value=False)
        self.attribute_update_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(options_frame, text='Spatial Overlay Analysis (Point-in-Polygon)', 
                       variable=self.spatial_overlay_var).grid(row=0, column=0, sticky='w', pady=2)
        ttk.Checkbutton(options_frame, text='HCV Category Processing', 
                       variable=self.hcv_processing_var).grid(row=1, column=0, sticky='w', pady=2)
        ttk.Checkbutton(options_frame, text='Area Calculations (Total, Unplanted, Net)', 
                       variable=self.area_calculation_var).grid(row=2, column=0, sticky='w', pady=2)
        ttk.Checkbutton(options_frame, text='Advanced Polygon Hole Creation', 
                       variable=self.hole_creation_var).grid(row=3, column=0, sticky='w', pady=2)
        ttk.Checkbutton(options_frame, text='Attribute Table Updates', 
                       variable=self.attribute_update_var).grid(row=4, column=0, sticky='w', pady=2)
        
        # Processing controls
        control_frame = ttk.LabelFrame(self.analysis_frame, text='Processing Controls', padding=10)
        control_frame.pack(fill='x', padx=10, pady=5)
        
        self.start_btn = ttk.Button(control_frame, text='Start Analysis', command=self.start_analysis)
        self.start_btn.pack(side='left', padx=5)
        
        self.cancel_btn = ttk.Button(control_frame, text='Cancel', command=self.cancel_analysis, state='disabled')
        self.cancel_btn.pack(side='left', padx=5)
        
        # Progress section
        progress_frame = ttk.LabelFrame(self.analysis_frame, text='Progress', padding=10)
        progress_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill='x', pady=5)
        
        # Status label
        self.status_var = tk.StringVar(value='Ready')
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        self.status_label.pack(pady=5)

    def create_results_tab(self):
        """Create results display tab"""
        self.results_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.results_frame, text='Analysis Results')
        
        # Summary statistics
        stats_frame = ttk.LabelFrame(self.results_frame, text='Analysis Summary', padding=10)
        stats_frame.pack(fill='x', padx=10, pady=5)
        
        self.stats_text = scrolledtext.ScrolledText(stats_frame, height=6)
        self.stats_text.pack(fill='x')
        
        # Detailed results table
        table_frame = ttk.LabelFrame(self.results_frame, text='Detailed Results by Block/Sub Division', padding=10)
        table_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Results treeview
        result_columns = ('BLOK', 'SUB_DIVISI', 'Total_Area_Ha', 'Unplanted_Area_Ha', 'Net_Area_Ha', 
                         'Tree_Count', 'HCV0_Polygons', 'HCV1_Polygons')
        self.results_tree = ttk.Treeview(table_frame, columns=result_columns, show='headings', height=15)
        
        for col in result_columns:
            self.results_tree.heading(col, text=col.replace('_', ' '))
            self.results_tree.column(col, width=120)
        
        # Scrollbars for results
        results_v_scroll = ttk.Scrollbar(table_frame, orient='vertical', command=self.results_tree.yview)
        results_h_scroll = ttk.Scrollbar(table_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=results_v_scroll.set, xscrollcommand=results_h_scroll.set)
        
        self.results_tree.pack(side='left', fill='both', expand=True)
        results_v_scroll.pack(side='right', fill='y')
        
        # Export controls
        export_frame = ttk.Frame(self.results_frame)
        export_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(export_frame, text='Export to Excel', command=self.export_excel).pack(side='left', padx=5)
        ttk.Button(export_frame, text='Export to Shapefile', command=self.export_shapefile).pack(side='left', padx=5)
        ttk.Button(export_frame, text='Export Summary to CSV', command=self.export_csv).pack(side='left', padx=5)

    def create_log_tab(self):
        """Create logging tab"""
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text='Processing Log')
        
        # Log display
        log_display_frame = ttk.LabelFrame(self.log_frame, text='Processing Log', padding=10)
        log_display_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_display_frame, height=30, state='disabled')
        self.log_text.pack(fill='both', expand=True)
        
        # Log controls
        log_control_frame = ttk.Frame(self.log_frame)
        log_control_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Button(log_control_frame, text='Clear Log', command=self.clear_log).pack(side='left', padx=5)
        ttk.Button(log_control_frame, text='Save Log', command=self.save_log).pack(side='left', padx=5)

    def browse_boundary(self):
        """Browse for boundary shapefile"""
        path = filedialog.askopenfilename(
            title='Select Boundary Polygon Shapefile',
            filetypes=[('Shapefile', '*.shp'), ('All files', '*.*')]
        )
        if path:
            self.boundary_path = path
            self.boundary_entry.delete(0, 'end')
            self.boundary_entry.insert(0, path)

    def browse_points(self):
        """Browse for points shapefile"""
        path = filedialog.askopenfilename(
            title='Select Detection Points Shapefile',
            filetypes=[('Shapefile', '*.shp'), ('All files', '*.*')]
        )
        if path:
            self.points_path = path
            self.points_entry.delete(0, 'end')
            self.points_entry.insert(0, path)

    def load_initial_data(self):
        """Load initial data from shapefiles"""
        self.log_message("Loading initial data...")
        
        try:
            # Get paths from entries
            self.boundary_path = self.boundary_entry.get()
            self.points_path = self.points_entry.get()
            
            # Check file existence
            if not os.path.exists(self.boundary_path):
                self.log_message(f"ERROR: Boundary file not found: {self.boundary_path}", 'ERROR')
                return
            
            if not os.path.exists(self.points_path):
                self.log_message(f"ERROR: Points file not found: {self.points_path}", 'ERROR')
                return
            
            # Load boundary shapefile
            self.log_message("Loading boundary polygons...")
            self.gdf_boundary = gpd.read_file(self.boundary_path)
            self.log_message(f"✓ Loaded {len(self.gdf_boundary)} boundary polygons")
            self.log_message(f"  CRS: {self.gdf_boundary.crs}")
            self.log_message(f"  Columns: {list(self.gdf_boundary.columns)}")
            
            # Load detection points
            self.log_message("Loading detection points...")
            self.gdf_points = gpd.read_file(self.points_path)
            self.log_message(f"✓ Loaded {len(self.gdf_points)} detection points")
            self.log_message(f"  CRS: {self.gdf_points.crs}")
            
            # Standardize column names
            self.standardize_column_names()
            
            # Update preview
            self.update_data_preview()
            
            self.status_var.set('Data loaded successfully')
            self.log_message("Initial data loading completed successfully")
            
        except Exception as e:
            error_msg = f"ERROR loading data: {str(e)}"
            self.log_message(error_msg, 'ERROR')
            self.status_var.set('Error loading data')
            messagebox.showerror("Error", error_msg)

    def standardize_column_names(self):
        """Standardize column names for consistent processing"""
        self.log_message("Standardizing column names...")
        
        # Boundary data column mappings
        boundary_mappings = {
            'SUB_DIVISI': 'SUBDIVISI',
            'HCV_Catego': 'HCV',
            'Jumlah_Poh': 'JUMLAH_POH',
            'HCV_catego': 'HCV'
        }
        
        # Apply mappings to boundary data
        for old_name, new_name in boundary_mappings.items():
            if old_name in self.gdf_boundary.columns and new_name not in self.gdf_boundary.columns:
                self.gdf_boundary = self.gdf_boundary.rename(columns={old_name: new_name})
                self.log_message(f"  Renamed '{old_name}' to '{new_name}' in boundary data")
        
        # Clean data types
        self.clean_data_types()

    def clean_data_types(self):
        """Clean and standardize data types"""
        # Numeric columns
        numeric_columns = ['JUMLAH_POH', 'LUAS_AUTO', 'luas_aut_1', 'total_incl', 'luas_netto']
        
        for col in numeric_columns:
            if col in self.gdf_boundary.columns:
                self.gdf_boundary[col] = pd.to_numeric(self.gdf_boundary[col], errors='coerce').fillna(0)
        
        # String columns
        if 'HCV' in self.gdf_boundary.columns:
            self.gdf_boundary['HCV'] = self.gdf_boundary['HCV'].astype(str)

    def update_data_preview(self):
        """Update data preview in the GUI"""
        # Clear existing data
        for item in self.boundary_tree.get_children():
            self.boundary_tree.delete(item)
        
        if self.gdf_boundary is not None:
            # Show first 10 records
            preview_columns = ['SUBDIVISI', 'BLOK', 'LUAS_AUTO', 'JUMLAH_POH', 'HCV', 'ID_Feature']
            available_columns = [col for col in preview_columns if col in self.gdf_boundary.columns]
            
            for idx, row in self.gdf_boundary.head(10).iterrows():
                values = []
                for col in preview_columns:
                    if col in self.gdf_boundary.columns:
                        value = row[col]
                        if pd.isna(value):
                            values.append('N/A')
                        elif isinstance(value, float):
                            values.append(f"{value:.2f}")
                        else:
                            values.append(str(value))
                    else:
                        values.append('N/A')
                
                self.boundary_tree.insert('', 'end', values=values)

    def validate_data(self):
        """Validate loaded data for processing"""
        self.log_message("=== DATA VALIDATION ===")
        
        if self.gdf_boundary is None or self.gdf_points is None:
            self.log_message("ERROR: Data not loaded. Please load data first.", 'ERROR')
            return False
        
        # Check required columns
        required_columns = ['BLOK', 'SUBDIVISI', 'geometry']
        missing_columns = [col for col in required_columns if col not in self.gdf_boundary.columns]
        
        if missing_columns:
            self.log_message(f"ERROR: Missing required columns: {missing_columns}", 'ERROR')
            return False
        
        # Check HCV categories
        if 'HCV' in self.gdf_boundary.columns:
            hcv_values = self.gdf_boundary['HCV'].unique()
            self.log_message(f"HCV categories found: {hcv_values}")
            
            # Count HCV types
            for hcv in hcv_values:
                count = len(self.gdf_boundary[self.gdf_boundary['HCV'] == hcv])
                self.log_message(f"  HCV {hcv}: {count} polygons")
        
        # Check CRS compatibility
        if self.gdf_boundary.crs != self.gdf_points.crs:
            self.log_message(f"WARNING: CRS mismatch detected", 'WARNING')
            self.log_message(f"  Boundary CRS: {self.gdf_boundary.crs}")
            self.log_message(f"  Points CRS: {self.gdf_points.crs}")
            self.log_message("  Will auto-convert during processing")
        
        self.log_message("✓ Data validation completed")
        return True

    def start_analysis(self):
        """Start the spatial analysis process"""
        if self.processing_active:
            return
        
        if not self.validate_data():
            return
        
        self.processing_active = True
        self.cancel_processing = False
        self.start_btn.config(state='disabled')
        self.cancel_btn.config(state='normal')
        
        # Start analysis in separate thread
        analysis_thread = threading.Thread(target=self.run_comprehensive_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def cancel_analysis(self):
        """Cancel ongoing analysis"""
        self.cancel_processing = True
        self.log_message("Analysis cancellation requested...")
        self.status_var.set("Cancelling...")

    def run_comprehensive_analysis(self):
        """Run the complete comprehensive analysis"""
        try:
            self.log_message("=== STARTING COMPREHENSIVE SPATIAL ANALYSIS ===")
            self.update_progress(0, "Initializing analysis...")
            
            # Copy data for processing
            gdf = self.gdf_boundary.copy()
            points = self.gdf_points.copy()
            
            # Ensure CRS compatibility
            if gdf.crs != points.crs:
                self.log_message(f"Converting points CRS: {points.crs} -> {gdf.crs}")
                points = points.to_crs(gdf.crs)
            
            # Step 1: Spatial Overlay Analysis
            if self.spatial_overlay_var.get() and not self.cancel_processing:
                self.update_progress(10, "Performing spatial overlay analysis...")
                gdf = self.perform_spatial_overlay(gdf, points)
            
            # Step 2: HCV Category Processing
            if self.hcv_processing_var.get() and not self.cancel_processing:
                self.update_progress(30, "Processing HCV categories...")
                self.process_hcv_categories(gdf)
            
            # Step 3: Area Calculations
            if self.area_calculation_var.get() and not self.cancel_processing:
                self.update_progress(50, "Calculating areas...")
                gdf = self.calculate_comprehensive_areas(gdf)
            
            # Step 4: Polygon Hole Creation
            if self.hole_creation_var.get() and not self.cancel_processing:
                self.update_progress(70, "Creating polygon holes...")
                gdf = self.create_polygon_holes(gdf)
            
            # Step 5: Attribute Updates
            if self.attribute_update_var.get() and not self.cancel_processing:
                self.update_progress(85, "Updating attributes...")
                gdf = self.update_attributes(gdf)
            
            # Finalize results
            if not self.cancel_processing:
                self.update_progress(95, "Finalizing results...")
                self.processed_gdf = gdf
                self.generate_analysis_summary()
                self.update_results_display()
                
                self.update_progress(100, "Analysis completed successfully")
                self.log_message("=== COMPREHENSIVE ANALYSIS COMPLETED ===")
            
        except Exception as e:
            error_msg = f"Error during analysis: {str(e)}"
            self.log_message(error_msg, 'ERROR')
            self.log_message(traceback.format_exc(), 'ERROR')
            self.status_var.set("Analysis failed")
            
        finally:
            # Re-enable controls
            self.processing_active = False
            self.start_btn.config(state='normal')
            self.cancel_btn.config(state='disabled')

    def update_progress(self, value, status_text):
        """Update progress bar and status"""
        self.progress_var.set(value)
        self.status_var.set(status_text)
        self.root.update_idletasks()

    def perform_spatial_overlay(self, gdf, points):
        """Perform spatial overlay analysis"""
        self.log_message("Performing spatial overlay (point-in-polygon)...")
        
        # Filter for boundary polygons (HCV=0 or containing 'Boundary')
        if 'HCV' in gdf.columns:
            boundary_mask = (gdf['HCV'] == '0') | (gdf['HCV'].str.contains('Boundary', na=False))
            boundary_polygons = gdf[boundary_mask].copy()
        else:
            # If no HCV column, use all polygons
            boundary_polygons = gdf.copy()
        
        if len(boundary_polygons) == 0:
            self.log_message("No boundary polygons found, using all polygons")
            boundary_polygons = gdf.copy()
        
        self.log_message(f"Using {len(boundary_polygons)} polygons for tree counting")
        
        # Perform spatial join
        overlay_result = gpd.sjoin(points, boundary_polygons, how='inner', predicate='within')
        self.log_message(f"Found {len(overlay_result)} detection points within boundaries")
        
        # Count trees per polygon
        if 'BLOK' in boundary_polygons.columns and 'SUBDIVISI' in boundary_polygons.columns:
            tree_counts = overlay_result.groupby(['BLOK', 'SUBDIVISI']).size().reset_index(name='tree_count_new')
        else:
            tree_counts = overlay_result.groupby('index_right').size().reset_index(name='tree_count_new')
        
        # Update tree counts in original data
        if 'BLOK' in gdf.columns and 'SUBDIVISI' in gdf.columns:
            gdf = gdf.merge(tree_counts, on=['BLOK', 'SUBDIVISI'], how='left')
            gdf['tree_count_new'] = gdf['tree_count_new'].fillna(0).astype(int)
            gdf['JUMLAH_POH'] = gdf['tree_count_new']
        
        total_trees = tree_counts['tree_count_new'].sum() if 'tree_count_new' in tree_counts.columns else 0
        self.log_message(f"Total trees counted: {total_trees}")
        
        return gdf

    def process_hcv_categories(self, gdf):
        """Process HCV categories for area calculations"""
        self.log_message("Processing HCV categories...")
        
        if 'HCV' not in gdf.columns:
            self.log_message("No HCV column found, skipping HCV processing")
            return
        
        # Group by Block and Sub Division
        grouped = gdf.groupby(['BLOK', 'SUBDIVISI'])
        
        results = []
        
        for (blok, subdivisi), group in grouped:
            if self.cancel_processing:
                break
            
            # Separate HCV categories
            hcv_0 = group[group['HCV'] == '0']  # Boundaries
            hcv_1 = group[group['HCV'] == '1']  # Enclaves/unplanted
            
            # Calculate areas
            total_area = 0
            unplanted_area = 0
            
            if len(hcv_0) > 0:
                if 'LUAS_AUTO' in hcv_0.columns:
                    total_area = hcv_0['LUAS_AUTO'].sum()
                else:
                    total_area = hcv_0.geometry.area.sum() / 10000
            
            if len(hcv_1) > 0:
                if 'LUAS_AUTO' in hcv_1.columns:
                    unplanted_area = hcv_1['LUAS_AUTO'].sum()
                else:
                    unplanted_area = hcv_1.geometry.area.sum() / 10000
            
            net_area = total_area - unplanted_area
            
            # Get tree count
            tree_count = hcv_0['JUMLAH_POH'].sum() if 'JUMLAH_POH' in hcv_0.columns else 0
            
            result = {
                'BLOK': blok,
                'SUBDIVISI': subdivisi,
                'total_area_ha': total_area,
                'unplanted_area_ha': unplanted_area,
                'net_area_ha': net_area,
                'tree_count': tree_count,
                'hcv_0_polygons': len(hcv_0),
                'hcv_1_polygons': len(hcv_1)
            }
            
            results.append(result)
            
            self.log_message(f"  {blok}-{subdivisi}: {total_area:.2f}ha total, "
                           f"{unplanted_area:.2f}ha unplanted, {net_area:.2f}ha net, {tree_count} trees")
        
        # Store results
        self.analysis_summary = pd.DataFrame(results)
        self.log_message(f"Processed {len(results)} Block/Sub Division combinations")

    def calculate_comprehensive_areas(self, gdf):
        """Calculate comprehensive area measurements"""
        self.log_message("Calculating comprehensive areas...")
        
        # Ensure geometry is in projected CRS for area calculation
        if not gdf.crs or not gdf.crs.is_projected:
            self.log_message("Converting to projected CRS for area calculation...")
            gdf = gdf.to_crs(epsg=32748)  # UTM Zone 48S
        
        # Calculate geometry-based area
        gdf['geom_area_sqm'] = gdf.geometry.area
        gdf['geom_area_ha'] = gdf['geom_area_sqm'] / 10000
        
        # Update luas_total if not present or zero
        if 'luas_total' not in gdf.columns:
            gdf['luas_total'] = gdf['geom_area_ha']
        else:
            # Fill zero values with calculated area
            zero_mask = (gdf['luas_total'] == 0) | pd.isna(gdf['luas_total'])
            gdf.loc[zero_mask, 'luas_total'] = gdf.loc[zero_mask, 'geom_area_ha']
        
        self.log_message("Area calculations completed")
        return gdf

    def create_polygon_holes(self, gdf):
        """Create holes in polygons where enclaves exist"""
        self.log_message("Creating polygon holes (advanced geometric processing)...")
        
        if 'HCV' not in gdf.columns:
            self.log_message("No HCV column found, skipping hole creation")
            return gdf
        
        try:
            gdf = gdf.copy()
            
            # Group by Block and Sub Division
            for (blok, subdivisi), group in gdf.groupby(['BLOK', 'SUBDIVISI']):
                if self.cancel_processing:
                    break
                
                boundaries = group[group['HCV'] == '0']
                enclaves = group[group['HCV'] == '1']
                
                if len(boundaries) == 0 or len(enclaves) == 0:
                    continue
                
                # Create union of all enclaves
                enclave_union = unary_union(enclaves.geometry.tolist())
                
                # Process each boundary polygon
                for idx, boundary_row in boundaries.iterrows():
                    boundary_geom = boundary_row.geometry
                    
                    if boundary_geom.contains(enclave_union):
                        try:
                            # Create polygon with holes
                            if hasattr(enclave_union, 'geoms'):
                                holes = [geom.exterior.coords for geom in enclave_union.geoms 
                                        if hasattr(geom, 'exterior')]
                            else:
                                holes = [enclave_union.exterior.coords] if hasattr(enclave_union, 'exterior') else []
                            
                            if holes:
                                new_geom = Polygon(boundary_geom.exterior.coords, holes)
                                gdf.at[idx, 'geometry'] = new_geom
                                self.log_message(f"  Created hole in {blok}-{subdivisi}")
                        
                        except Exception as e:
                            self.log_message(f"  Warning: Could not create hole for {blok}-{subdivisi}: {e}")
            
            self.log_message("Polygon hole creation completed")
            
        except Exception as e:
            self.log_message(f"Error in hole creation: {e}", 'ERROR')
        
        return gdf

    def update_attributes(self, gdf):
        """Update attribute table with calculated values"""
        self.log_message("Updating attribute table...")
        
        # Ensure required columns exist
        required_columns = ['luas_total', 'luas_inclave', 'luas_netto', 'JUMLAH_POH']
        
        for col in required_columns:
            if col not in gdf.columns:
                gdf[col] = 0
        
        # Update based on analysis summary if available
        if self.analysis_summary is not None:
            for _, row in self.analysis_summary.iterrows():
                mask = (gdf['BLOK'] == row['BLOK']) & (gdf['SUBDIVISI'] == row['SUBDIVISI'])
                
                if mask.any():
                    gdf.loc[mask, 'luas_total'] = row['total_area_ha']
                    gdf.loc[mask, 'luas_inclave'] = row['unplanted_area_ha']
                    gdf.loc[mask, 'luas_netto'] = row['net_area_ha']
                    gdf.loc[mask, 'JUMLAH_POH'] = row['tree_count']
        
        self.log_message("Attribute updates completed")
        return gdf

    def generate_analysis_summary(self):
        """Generate comprehensive analysis summary"""
        if self.analysis_summary is None or self.processed_gdf is None:
            return
        
        summary_text = "=== ANALYSIS SUMMARY ===\n\n"
        
        # Overall statistics
        total_area = self.analysis_summary['total_area_ha'].sum()
        total_unplanted = self.analysis_summary['unplanted_area_ha'].sum()
        total_net = self.analysis_summary['net_area_ha'].sum()
        total_trees = self.analysis_summary['tree_count'].sum()
        
        summary_text += f"Total Area: {total_area:.2f} hectares\n"
        summary_text += f"Total Unplanted Area: {total_unplanted:.2f} hectares\n"
        summary_text += f"Total Net Plantable Area: {total_net:.2f} hectares\n"
        summary_text += f"Total Trees Detected: {total_trees:,}\n"
        summary_text += f"Tree Density: {total_trees/total_net:.2f} trees/ha\n\n"
        
        # Block summary
        summary_text += "Summary by Block:\n"
        block_summary = self.analysis_summary.groupby('BLOK').agg({
            'total_area_ha': 'sum',
            'unplanted_area_ha': 'sum',
            'net_area_ha': 'sum',
            'tree_count': 'sum'
        }).round(2)
        
        for blok, row in block_summary.iterrows():
            summary_text += f"  {blok}: {row['net_area_ha']:.2f}ha net, {row['tree_count']} trees\n"
        
        # Update summary display
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, summary_text)

    def update_results_display(self):
        """Update the results table display"""
        # Clear existing data
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        if self.analysis_summary is not None:
            for _, row in self.analysis_summary.iterrows():
                values = (
                    row['BLOK'],
                    row['SUBDIVISI'],
                    f"{row['total_area_ha']:.2f}",
                    f"{row['unplanted_area_ha']:.2f}",
                    f"{row['net_area_ha']:.2f}",
                    int(row['tree_count']),
                    int(row['hcv_0_polygons']),
                    int(row['hcv_1_polygons'])
                )
                self.results_tree.insert('', 'end', values=values)

    def export_excel(self):
        """Export results to Excel"""
        if self.analysis_summary is None:
            messagebox.showerror('Error', 'No analysis results to export!')
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension='.xlsx',
            filetypes=[('Excel files', '*.xlsx'), ('All files', '*.*')],
            title='Export Analysis Results to Excel'
        )
        
        if filename:
            try:
                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    self.analysis_summary.to_excel(writer, sheet_name='Analysis Summary', index=False)
                    
                    if self.processed_gdf is not None:
                        # Export attribute data (without geometry)
                        attr_df = pd.DataFrame(self.processed_gdf.drop(columns='geometry'))
                        attr_df.to_excel(writer, sheet_name='Detailed Attributes', index=False)
                
                self.log_message(f"Results exported to Excel: {filename}")
                messagebox.showinfo('Success', f'Results exported to:\n{filename}')
                
            except Exception as e:
                error_msg = f"Error exporting to Excel: {str(e)}"
                self.log_message(error_msg, 'ERROR')
                messagebox.showerror('Error', error_msg)

    def export_shapefile(self):
        """Export processed shapefile with aggregated geometries and updated attributes."""
        if self.processed_gdf is None:
            messagebox.showerror('Error', 'No processed data to export!')
            return

        filename = filedialog.asksaveasfilename(
            defaultextension='.shp',
            filetypes=[('Shapefile', '*.shp'), ('All files', '*.*')],
            title='Export Aggregated Shapefile'
        )

        if not filename:
            return

        try:
            self.log_message("Aggregating data for shapefile export...")
            
            # Define aggregation functions for attributes.
            # The values were set per group in 'update_attributes', so 'first' is appropriate.
            agg_dict = {
                'luas_total': 'first',
                'luas_inclave': 'first',
                'luas_netto': 'first',
                'JUMLAH_POH': 'first'
            }
            
            # Filter for columns that actually exist in the dataframe to avoid errors
            existing_agg_dict = {k: v for k, v in agg_dict.items() if k in self.processed_gdf.columns}

            # Dissolve polygons by BLOK and SUBDIVISI, aggregating attributes
            # The geometry is automatically unioned by default in dissolve.
            aggregated_gdf = self.processed_gdf.dissolve(by=['BLOK', 'SUBDIVISI'], aggfunc=existing_agg_dict)
            
            # Rename columns for clarity in the output shapefile
            aggregated_gdf = aggregated_gdf.rename(columns={
                'luas_total': 'Luas_Total',
                'luas_inclave': 'Luas_Incla',
                'luas_netto': 'Luas_Netto',
                'JUMLAH_POH': 'Jml_Pohon'
            })
            
            # Reset index to get BLOK and SUBDIVISI as columns instead of index
            aggregated_gdf = aggregated_gdf.reset_index()

            # Ensure the CRS is carried over
            aggregated_gdf.crs = self.processed_gdf.crs

            # Export to shapefile
            aggregated_gdf.to_file(filename, driver='ESRI Shapefile')
            
            self.log_message(f"Aggregated shapefile exported: {filename}")
            messagebox.showinfo('Success', f'Aggregated shapefile exported to:\n{filename}')

        except Exception as e:
            # Add traceback for better debugging
            error_msg = f"Error exporting aggregated shapefile: {str(e)}\n{traceback.format_exc()}"
            self.log_message(error_msg, 'ERROR')
            messagebox.showerror('Error', error_msg)

    def export_csv(self):
        """Export summary to CSV"""
        if self.analysis_summary is None:
            messagebox.showerror('Error', 'No analysis summary to export!')
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension='.csv',
            filetypes=[('CSV files', '*.csv'), ('All files', '*.*')],
            title='Export Summary to CSV'
        )
        
        if filename:
            try:
                self.analysis_summary.to_csv(filename, index=False)
                self.log_message(f"Summary exported to CSV: {filename}")
                messagebox.showinfo('Success', f'Summary exported to:\n{filename}')
                
            except Exception as e:
                error_msg = f"Error exporting to CSV: {str(e)}"
                self.log_message(error_msg, 'ERROR')
                messagebox.showerror('Error', error_msg)

    def clear_log(self):
        """Clear the log display"""
        self.log_text.configure(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.configure(state='disabled')

    def save_log(self):
        """Save log to file"""
        filename = filedialog.asksaveasfilename(
            defaultextension='.txt',
            filetypes=[('Text files', '*.txt'), ('All files', '*.*')],
            title='Save Log File'
        )
        
        if filename:
            try:
                log_content = self.log_text.get(1.0, tk.END)
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                
                self.log_message(f"Log saved to: {filename}")
                messagebox.showinfo('Success', f'Log saved to:\n{filename}')
                
            except Exception as e:
                error_msg = f"Error saving log: {str(e)}"
                self.log_message(error_msg, 'ERROR')
                messagebox.showerror('Error', error_msg)

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = ARECComprehensiveSpatialGUI(root)
    root.mainloop()

if __name__ == '__main__':
    main() 
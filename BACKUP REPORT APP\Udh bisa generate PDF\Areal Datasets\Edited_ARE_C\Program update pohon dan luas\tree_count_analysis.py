import geopandas as gpd
import pandas as pd
from shapely.geometry import Point
import os

def count_trees_in_polygons(polygon_path, points_path, output_path=None):
    """
    Menghitung jumlah titik pohon dalam setiap polygon blok
    
    Parameters:
    - polygon_path: Path ke shapefile polygon blok
    - points_path: Path ke shapefile titik deteksi pohon
    - output_path: Path untuk menyimpan hasil (optional)
    
    Returns:
    - GeoDataFrame dengan field tambahan jumlah pohon
    """
    
    print("Membaca file shapefile...")
    
    # Baca shapefile polygon blok
    try:
        polygons = gpd.read_file(polygon_path)
        print(f"✓ Berhasil membaca {len(polygons)} polygon blok")
        print(f"Kolom yang tersedia: {list(polygons.columns)}")
        
        # Pastikan ada kolom BLOK
        if 'BLOK' not in polygons.columns:
            print("Warning: Kolom 'BLOK' tidak ditemukan dalam shapefile polygon")
            print("Kolom yang tersedia:", list(polygons.columns))
    except Exception as e:
        print(f"Error membaca polygon shapefile: {e}")
        return None
    
    # Baca shapefile titik deteksi pohon
    try:
        points = gpd.read_file(points_path)
        print(f"✓ Berhasil membaca {len(points)} titik deteksi pohon")
    except Exception as e:
        print(f"Error membaca points shapefile: {e}")
        return None
    
    # Pastikan CRS sama
    if polygons.crs != points.crs:
        print(f"Menyamakan CRS: {polygons.crs} -> {points.crs}")
        points = points.to_crs(polygons.crs)
    
    print("\nMelakukan spatial join...")
    
    # Spatial join untuk menghitung titik dalam polygon
    # Menggunakan 'within' untuk menghitung titik yang berada dalam polygon
    joined = gpd.sjoin(points, polygons, how='inner', predicate='within')
    
    # Hitung jumlah pohon per blok
    tree_counts = joined.groupby(joined.index_right).size().reset_index(name='JUMLAH_POHON')
    tree_counts.rename(columns={'index_right': 'polygon_index'}, inplace=True)
    
    print(f"✓ Spatial join selesai. Ditemukan {len(joined)} pohon dalam polygon")
    
    # Gabungkan hasil dengan polygon asli
    polygons_result = polygons.copy()
    polygons_result['polygon_index'] = polygons_result.index
    
    # Merge dengan hasil penghitungan
    polygons_result = polygons_result.merge(
        tree_counts, 
        on='polygon_index', 
        how='left'
    )
    
    # Isi nilai kosong dengan 0 (untuk polygon yang tidak ada pohonnya)
    polygons_result['JUMLAH_POHON'] = polygons_result['JUMLAH_POHON'].fillna(0).astype(int)
    
    # Hapus kolom sementara
    polygons_result.drop('polygon_index', axis=1, inplace=True)
    
    print("\nHasil penghitungan per blok:")
    for idx, row in polygons_result.iterrows():
        print(f"Blok {row['BLOK']}: {row['JUMLAH_POHON']} pohon")
    
    print(f"\nTotal pohon: {polygons_result['JUMLAH_POHON'].sum()}")
    
    # Simpan hasil jika path output disediakan
    if output_path:
        try:
            polygons_result.to_file(output_path)
            print(f"✓ Hasil disimpan ke: {output_path}")
        except Exception as e:
            print(f"Error menyimpan file: {e}")
    
    return polygons_result

def main():
    """
    Fungsi utama untuk menjalankan analisis
    """
    
    # Path file shapefile
    polygon_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Polygon_Tambahan_ARE_C.shp"
    
    points_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"
    
    # Path output (akan disimpan di folder yang sama dengan script)
    output_path = "Polygon_ARE_C_dengan_Jumlah_Pohon.shp"
    
    print("=== ANALISIS PENGHITUNGAN POHON PER BLOK ===")
    print(f"Polygon blok: {polygon_path}")
    print(f"Titik deteksi: {points_path}")
    print(f"Output: {output_path}")
    print("=" * 50)
    
    # Periksa keberadaan file
    if not os.path.exists(polygon_path):
        print(f"Error: File polygon tidak ditemukan: {polygon_path}")
        return
    
    if not os.path.exists(points_path):
        print(f"Error: File points tidak ditemukan: {points_path}")
        return
    
    # Jalankan analisis
    result = count_trees_in_polygons(polygon_path, points_path, output_path)
    
    if result is not None:
        print("\n=== ANALISIS SELESAI ===")
        print("Shapefile baru telah dibuat dengan field tambahan 'JUMLAH_POHON'")
        
        # Tampilkan ringkasan statistik
        print(f"\nSTATISTIK PENGHITUNGAN:")
        print(f"Total blok: {len(result)}")
        print(f"Total pohon: {result['JUMLAH_POHON'].sum()}")
        print(f"Rata-rata pohon per blok: {result['JUMLAH_POHON'].mean():.2f}")
        print(f"Blok dengan pohon terbanyak: {result['JUMLAH_POHON'].max()} pohon")
        print(f"Blok dengan pohon tersedikit: {result['JUMLAH_POHON'].min()} pohon")
    else:
        print("\n=== ANALISIS GAGAL ===")

if __name__ == "__main__":
    main() 
"""
DIRECT HOLE CREATION TEST & FIX
Tests the exact issue you're experiencing and provides immediate fix
"""

import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon, MultiPolygon, GeometryCollection
from shapely.ops import unary_union
import os

def test_hole_creation_issue():
    """Test the exact hole creation issue you're experiencing"""
    print("🔧 DIRECT HOLE CREATION ISSUE TEST")
    print("=" * 50)
    
    # Use your actual file path
    shapefile_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Polygon_ARE_C_dengan_Jumlah_Pohon_update_atribut_tabe;.shp"
    
    if not os.path.exists(shapefile_path):
        print(f"❌ File not found: {shapefile_path}")
        return None
    
    try:
        # Load data
        print("📂 Loading shapefile...")
        gdf = gpd.read_file(shapefile_path)
        print(f"✅ Loaded {len(gdf)} features")
        
        # Standardize column names
        column_mappings = {
            'SUB_DIVISI': 'SUBDIVISI',
            'HCV_Catego': 'HCV',
            'Jumlah_Poh': 'JUMLAH_POH'
        }
        
        for old, new in column_mappings.items():
            if old in gdf.columns and new not in gdf.columns:
                gdf = gdf.rename(columns={old: new})
        
        # Clean data types
        if 'HCV' in gdf.columns:
            gdf['HCV'] = pd.to_numeric(gdf['HCV'], errors='coerce').fillna(0).astype(int)
        
        # Analyze initial geometry types
        print("\n🔍 INITIAL GEOMETRY ANALYSIS:")
        geom_types = gdf.geometry.geom_type.value_counts()
        print(geom_types)
        
        # Test hole creation on specific problematic block
        print("\n🕳️  TESTING HOLE CREATION ON P 18 / 01-SUB DIVISI ARE C 3...")
        
        # Find the specific block mentioned in your error
        target_block = gdf[(gdf['BLOK'] == 'P 18 / 01') & (gdf['SUBDIVISI'] == 'SUB DIVISI ARE C 3')]
        
        if len(target_block) == 0:
            print("❌ Target block not found")
            return None
        
        print(f"Found {len(target_block)} features for target block")
        
        # Show HCV distribution
        hcv_counts = target_block['HCV'].value_counts().sort_index()
        print(f"HCV distribution: {dict(hcv_counts)}")
        
        # Test the CORRECTED hole creation
        gdf_with_holes = test_corrected_hole_creation(gdf.copy())
        
        # Analyze resulting geometry types
        print("\n🔍 RESULTING GEOMETRY ANALYSIS:")
        result_geom_types = gdf_with_holes.geometry.geom_type.value_counts()
        print(result_geom_types)
        
        # Check for problematic geometries
        problematic_geoms = []
        for idx, geom in enumerate(gdf_with_holes.geometry):
            if geom.geom_type in ['GeometryCollection', 'MultiPolygon']:
                problematic_geoms.append((idx, geom.geom_type, geom))
        
        print(f"\n⚠️  Found {len(problematic_geoms)} problematic geometries after hole creation")
        
        # Fix the problematic geometries
        print("\n🔧 FIXING PROBLEMATIC GEOMETRIES...")
        gdf_fixed = fix_geometry_issues_for_export(gdf_with_holes)
        
        # Test export
        print("\n💾 TESTING EXPORT...")
        test_export_success = test_export_fixed_shapefile(gdf_fixed)
        
        if test_export_success:
            print("\n✅ SUCCESS! Issue identified and fixed")
            print("The problem was GeometryCollection creation during hole creation")
            print("Solution: Convert all geometries to simple Polygon before export")
        else:
            print("\n❌ Export still failed")
        
        return gdf_fixed
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_corrected_hole_creation(gdf):
    """Test the CORRECTED hole creation that creates proper polygon areas"""
    print("Testing CORRECTED hole creation process...")
    
    # Group by BLOK and SUBDIVISI
    for (blok, subdivisi), group in gdf.groupby(['BLOK', 'SUBDIVISI']):
        boundary_features = group[group['HCV'] == 0]
        enclave_features = group[group['HCV'] == 1]
        
        if len(boundary_features) == 0 or len(enclave_features) == 0:
            continue
        
        print(f"  Processing {blok}-{subdivisi}: {len(boundary_features)} boundaries, {len(enclave_features)} enclaves")
        
        # For each boundary in this group
        for boundary_idx, boundary_row in boundary_features.iterrows():
            boundary_geom = boundary_row.geometry
            
            # Skip invalid geometries
            if boundary_geom is None or not hasattr(boundary_geom, 'is_valid') or not boundary_geom.is_valid:
                continue
            
            contained_enclaves = []
            
            # Check which enclaves are contained in this boundary
            for _, enclave_row in enclave_features.iterrows():
                enclave_geom = enclave_row.geometry
                
                if enclave_geom is None or not hasattr(enclave_geom, 'is_valid') or not enclave_geom.is_valid:
                    continue
                
                try:
                    if boundary_geom.contains(enclave_geom):
                        contained_enclaves.append(enclave_geom)
                except Exception as geom_error:
                    continue
            
            # Create holes for contained enclaves
            if contained_enclaves:
                try:
                    # CORRECTED APPROACH: Use difference operation instead of Polygon constructor
                    print(f"    Creating holes for {blok}-{subdivisi} with {len(contained_enclaves)} enclaves")
                    
                    # Start with the boundary polygon
                    result_polygon = boundary_geom
                    
                    # For each enclave, subtract it from the boundary
                    for enclave_geom in contained_enclaves:
                        try:
                            # Use difference operation to create proper polygon with holes
                            result_polygon = result_polygon.difference(enclave_geom)
                            
                            # Ensure result is still a valid polygon
                            if result_polygon.geom_type == 'MultiPolygon':
                                # If result is MultiPolygon, take the largest part
                                largest_poly = max(result_polygon.geoms, key=lambda p: p.area)
                                result_polygon = largest_poly
                            elif result_polygon.geom_type != 'Polygon':
                                # If result is not a polygon, skip this enclave
                                print(f"    Warning: Result is {result_polygon.geom_type}, skipping enclave")
                                continue
                                
                        except Exception as diff_error:
                            print(f"    Warning: Difference operation failed: {diff_error}")
                            continue
                    
                    # Update the geometry with the result
                    if result_polygon.geom_type == 'Polygon' and result_polygon.is_valid:
                        gdf.at[boundary_idx, 'geometry'] = result_polygon
                        print(f"    ✓ Created proper polygon with holes for {blok}-{subdivisi}")
                        print(f"    ✓ Result area: {result_polygon.area:.2f} sq units")
                    else:
                        print(f"    ⚠️  Invalid result geometry: {result_polygon.geom_type}")
                
                except Exception as hole_error:
                    print(f"    Error creating enclave holes for {blok}-{subdivisi}: {hole_error}")
    
    return gdf

def test_problematic_hole_creation(gdf):
    """Test the exact hole creation that's causing the issue"""
    print("Testing PROBLEMATIC hole creation process...")
    
    # Group by BLOK and SUBDIVISI
    for (blok, subdivisi), group in gdf.groupby(['BLOK', 'SUBDIVISI']):
        boundary_features = group[group['HCV'] == 0]
        enclave_features = group[group['HCV'] == 1]
        
        if len(boundary_features) == 0 or len(enclave_features) == 0:
            continue
        
        print(f"  Processing {blok}-{subdivisi}: {len(boundary_features)} boundaries, {len(enclave_features)} enclaves")
        
        # For each boundary in this group
        for boundary_idx, boundary_row in boundary_features.iterrows():
            boundary_geom = boundary_row.geometry
            
            # Skip invalid geometries
            if boundary_geom is None or not hasattr(boundary_geom, 'is_valid') or not boundary_geom.is_valid:
                continue
            
            contained_enclaves = []
            
            # Check which enclaves are contained in this boundary
            for _, enclave_row in enclave_features.iterrows():
                enclave_geom = enclave_row.geometry
                
                if enclave_geom is None or not hasattr(enclave_geom, 'is_valid') or not enclave_geom.is_valid:
                    continue
                
                try:
                    if boundary_geom.contains(enclave_geom):
                        contained_enclaves.append(enclave_geom)
                except Exception as geom_error:
                    continue
            
            # Create holes for contained enclaves
            if contained_enclaves:
                try:
                    holes = []
                    for enclave_geom in contained_enclaves:
                        if isinstance(enclave_geom, Polygon):
                            holes.append(list(enclave_geom.exterior.coords))
                        elif isinstance(enclave_geom, MultiPolygon):
                            for poly in enclave_geom.geoms:
                                if isinstance(poly, Polygon):
                                    holes.append(list(poly.exterior.coords))
                    
                    if holes:
                        # Handle boundary geometry type
                        if isinstance(boundary_geom, Polygon):
                            new_polygon = Polygon(boundary_geom.exterior.coords, holes)
                            gdf.at[boundary_idx, 'geometry'] = new_polygon
                            print(f"    ✓ Created {len(holes)} enclave holes in {blok}-{subdivisi}")
                        elif isinstance(boundary_geom, MultiPolygon):
                            # For MultiPolygon boundary, create holes in the largest polygon
                            largest_boundary = max(boundary_geom.geoms, key=lambda p: p.area)
                            new_polygon = Polygon(largest_boundary.exterior.coords, holes)
                            # Replace the entire MultiPolygon with the new holed polygon
                            gdf.at[boundary_idx, 'geometry'] = new_polygon
                            print(f"    ✓ Created {len(holes)} enclave holes in MultiPolygon {blok}-{subdivisi}")
                
                except Exception as hole_error:
                    print(f"    Error creating enclave holes for {blok}-{subdivisi}: {hole_error}")
    
    return gdf

def fix_geometry_issues_for_export(gdf):
    """Fix all geometry issues that prevent shapefile export"""
    print("Fixing geometry issues for export...")
    gdf_fixed = gdf.copy()
    
    fixed_count = 0
    for idx, geom in enumerate(gdf_fixed.geometry):
        try:
            if geom.geom_type == 'GeometryCollection':
                # Extract only Polygon/MultiPolygon from GeometryCollection
                polygons = [g for g in geom.geoms if g.geom_type in ['Polygon', 'MultiPolygon']]
                if polygons:
                    if len(polygons) == 1:
                        fixed_geom = polygons[0]
                    else:
                        # Union multiple polygons
                        fixed_geom = unary_union(polygons)
                    
                    # Ensure it's a simple Polygon
                    if fixed_geom.geom_type == 'MultiPolygon':
                        # Take the largest polygon
                        largest_poly = max(fixed_geom.geoms, key=lambda x: x.area)
                        fixed_geom = largest_poly
                    
                    gdf_fixed.at[idx, 'geometry'] = fixed_geom
                    fixed_count += 1
                    print(f"  Fixed GeometryCollection at index {idx}")
            
            elif geom.geom_type == 'MultiPolygon':
                # Convert MultiPolygon to single Polygon (largest one)
                largest_poly = max(geom.geoms, key=lambda x: x.area)
                gdf_fixed.at[idx, 'geometry'] = largest_poly
                fixed_count += 1
                print(f"  Converted MultiPolygon to Polygon at index {idx}")
            
            # Validate the fixed geometry
            if not gdf_fixed.at[idx, 'geometry'].is_valid:
                # Try to fix invalid geometry
                fixed_geom = gdf_fixed.at[idx, 'geometry'].buffer(0)
                gdf_fixed.at[idx, 'geometry'] = fixed_geom
                print(f"  Fixed invalid geometry at index {idx}")
        
        except Exception as e:
            print(f"  ⚠️  Could not fix geometry at index {idx}: {e}")
            continue
    
    print(f"✅ Fixed {fixed_count} geometries")
    
    # Final validation
    final_types = gdf_fixed.geometry.geom_type.value_counts()
    print("Final geometry types:")
    print(final_types)
    
    return gdf_fixed

def test_export_fixed_shapefile(gdf):
    """Test export with fixed geometries"""
    try:
        output_path = "test_hole_creation_fixed.shp"
        gdf.to_file(output_path)
        print(f"✅ Export test successful: {output_path}")
        
        # Verify by reading back
        test_read = gpd.read_file(output_path)
        print(f"✅ Read-back test successful: {len(test_read)} features")
        
        return True
    
    except Exception as e:
        print(f"❌ Export test failed: {e}")
        return False

def create_fixed_gui_method():
    """Create the fixed method for the GUI"""
    method_code = '''
def create_enclave_holes_corrected(self, gdf):
    """CORRECTED version that creates proper polygon areas with holes"""
    from shapely.geometry import Polygon, MultiPolygon
    holes_created = 0

    try:
        # Identify boundary and enclave polygons
        boundary_polygons = gdf[gdf['HCV'] == 0].copy()
        enclave_polygons = gdf[gdf['HCV'] == 1].copy()

        self.log(f"  Found {len(boundary_polygons)} boundary and {len(enclave_polygons)} enclave polygons")

        if len(boundary_polygons) == 0 or len(enclave_polygons) == 0:
            self.log("  No boundary or enclave polygons found for enclave hole creation")
            return 0

        # Group by BLOK and SUBDIVISI for efficient processing
        for (blok, subdivisi), group in gdf.groupby(['BLOK', 'SUBDIVISI']):
            group_boundaries = group[group['HCV'] == 0]
            group_enclaves = group[group['HCV'] == 1]

            if len(group_boundaries) == 0 or len(group_enclaves) == 0:
                continue

            self.log(f"  Processing {blok}-{subdivisi}: {len(group_boundaries)} boundaries, {len(group_enclaves)} enclaves")

            # For each boundary in this group
            for boundary_idx, boundary_row in group_boundaries.iterrows():
                boundary_geom = boundary_row.geometry
                
                # Skip invalid geometries
                if boundary_geom is None or not hasattr(boundary_geom, 'is_valid') or not boundary_geom.is_valid:
                    self.log(f"    Skipping invalid boundary geometry for {blok}-{subdivisi}")
                    continue

                contained_enclaves = []

                # Check which enclaves are contained in this boundary
                for _, enclave_row in group_enclaves.iterrows():
                    enclave_geom = enclave_row.geometry
                    
                    # Skip invalid geometries
                    if enclave_geom is None or not hasattr(enclave_geom, 'is_valid') or not enclave_geom.is_valid:
                        continue

                    try:
                        if boundary_geom.contains(enclave_geom):
                            contained_enclaves.append(enclave_geom)
                    except Exception as geom_error:
                        self.log(f"    Geometry error checking enclave containment: {geom_error}")
                        continue

                # Create holes for contained enclaves using CORRECTED approach
                if contained_enclaves:
                    try:
                        self.log(f"    Creating holes for {blok}-{subdivisi} with {len(contained_enclaves)} enclaves")
                        
                        # Start with the boundary polygon
                        result_polygon = boundary_geom
                        
                        # For each enclave, subtract it from the boundary
                        for enclave_geom in contained_enclaves:
                            try:
                                # Use difference operation to create proper polygon with holes
                                result_polygon = result_polygon.difference(enclave_geom)
                                
                                # Ensure result is still a valid polygon
                                if result_polygon.geom_type == 'MultiPolygon':
                                    # If result is MultiPolygon, take the largest part
                                    largest_poly = max(result_polygon.geoms, key=lambda p: p.area)
                                    result_polygon = largest_poly
                                elif result_polygon.geom_type != 'Polygon':
                                    # If result is not a polygon, skip this enclave
                                    self.log(f"    Warning: Result is {result_polygon.geom_type}, skipping enclave")
                                    continue
                                    
                            except Exception as diff_error:
                                self.log(f"    Warning: Difference operation failed: {diff_error}")
                                continue
                        
                        # Update the geometry with the result
                        if result_polygon.geom_type == 'Polygon' and result_polygon.is_valid:
                            gdf.at[boundary_idx, 'geometry'] = result_polygon
                            holes_created += len(contained_enclaves)
                            self.log(f"    ✓ Created proper polygon with holes for {blok}-{subdivisi}")
                            self.log(f"    ✓ Result area: {result_polygon.area:.2f} sq units")
                        else:
                            self.log(f"    ⚠️  Invalid result geometry: {result_polygon.geom_type}")

                    except Exception as hole_error:
                        self.log(f"    Error creating enclave holes for {blok}-{subdivisi}: {hole_error}")

        self.log(f"  Enclave holes created: {holes_created}")
        return holes_created

    except Exception as e:
        self.log(f"Error in enclave hole creation: {str(e)}")
        return 0
'''
    
    print("\n" + "="*60)
    print("CORRECTED HOLE CREATION METHOD:")
    print("="*60)
    print(method_code)
    print("="*60)
    print("Copy this method to replace the existing create_enclave_holes method")

def main():
    """Run the direct test"""
    print("🚀 DIRECT HOLE CREATION ISSUE TEST & FIX")
    print("This will reproduce your exact issue and provide the fix")
    print()
    
    # Run the test
    result = test_hole_creation_issue()
    
    if result is not None:
        print("\n✅ TEST COMPLETE - ISSUE IDENTIFIED AND FIXED")
        print("=" * 50)
        print("PROBLEM: Hole creation creates lines instead of polygon areas")
        print("SOLUTION: Use difference() operation instead of Polygon constructor")
        print("RESULT: Proper polygon areas with holes")
        print()
        
        # Provide the fixed method
        create_fixed_gui_method()
    else:
        print("\n❌ TEST FAILED")

if __name__ == "__main__":
    main() 
@echo off
echo ============================================
echo  TREE COUNTING - SPATIAL OVERLAY METHOD
echo ============================================
echo.

echo Script ini akan:
echo - Menggunakan polygon cleaned sebagai basis wilayah
echo - Melakukan spatial overlay dengan titik deteksi pohon
echo - Menghitung titik yang berada DI DALAM setiap polygon
echo - Membandingkan hasil lama vs baru
echo - Menyimpan hasil ke file baru
echo.

echo Memulai penghitungan overlay...
echo.

python tree_count_overlay_accurate.py
if errorlevel 1 (
    echo.
    echo ERROR: Script gagal dijalankan!
    echo Pastikan Python dan geopandas sudah terinstall.
    pause
    exit /b 1
)

echo.
echo ============================================
echo  PENGHITUNGAN OVERLAY SELESAI!
echo ============================================
echo.
echo File hasil: Polygon_ARE_C_TreeCount_Overlay.shp
echo Detail overlay: Polygon_ARE_C_TreeCount_Overlay_detail_overlay.csv
echo.
pause 
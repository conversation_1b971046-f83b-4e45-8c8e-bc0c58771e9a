"""
Test script for Advanced Polygon Hole Creation Features
This script tests the enhanced hole creation functionality
"""

import geopandas as gpd
import pandas as pd
import os
from shapely.geometry import Polygon, Point
import matplotlib.pyplot as plt

def test_hole_creation_logic():
    """Test the hole creation logic with sample data"""
    print("TESTING ADVANCED HOLE CREATION LOGIC")
    print("=" * 50)
    
    # Test file paths
    main_shapefile = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
    alt_shapefile = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Polygon_ARE_C_dengan_Jumlah_Pohon_update_atribut_tabe;.shp"
    
    # Test 1: Load and analyze main shapefile
    print("\n1. TESTING MAIN SHAPEFILE")
    print("-" * 30)
    
    if os.path.exists(main_shapefile):
        gdf_main = gpd.read_file(main_shapefile)
        analyze_shapefile_for_holes(gdf_main, "Main Shapefile")
    else:
        print(f"❌ Main shapefile not found: {main_shapefile}")
    
    # Test 2: Load and analyze alternative shapefile
    print("\n2. TESTING ALTERNATIVE SHAPEFILE")
    print("-" * 30)
    
    if os.path.exists(alt_shapefile):
        gdf_alt = gpd.read_file(alt_shapefile)
        analyze_shapefile_for_holes(gdf_alt, "Alternative Shapefile")
    else:
        print(f"❌ Alternative shapefile not found: {alt_shapefile}")
    
    # Test 3: Create synthetic test data
    print("\n3. TESTING WITH SYNTHETIC DATA")
    print("-" * 30)
    test_synthetic_hole_creation()

def analyze_shapefile_for_holes(gdf, name):
    """Analyze a shapefile for hole creation potential"""
    try:
        print(f"Analyzing {name}:")
        print(f"  Total features: {len(gdf)}")
        print(f"  Columns: {list(gdf.columns)}")
        
        # Standardize column names
        column_mappings = {
            'SUB_DIVISI': 'SUBDIVISI',
            'HCV_Catego': 'HCV',
            'Jumlah_Poh': 'JUMLAH_POH'
        }
        
        for old, new in column_mappings.items():
            if old in gdf.columns and new not in gdf.columns:
                gdf = gdf.rename(columns={old: new})
        
        # Clean HCV column
        if 'HCV' in gdf.columns:
            gdf['HCV'] = pd.to_numeric(gdf['HCV'], errors='coerce').fillna(0).astype(int)
            hcv_counts = gdf['HCV'].value_counts().sort_index()
            print(f"  HCV distribution: {dict(hcv_counts)}")
        
        # Check for ID_feature column
        if 'ID_feature' in gdf.columns:
            id_samples = gdf['ID_feature'].head(5).tolist()
            print(f"  ID_feature samples: {id_samples}")
            
            # Check for boundary patterns
            boundary_pattern_count = gdf['ID_feature'].astype(str).str.lower().str.startswith('boundary-').sum()
            print(f"  Features with 'boundary-' pattern: {boundary_pattern_count}")
        
        # Analyze hole creation potential
        analyze_boundary_in_boundary_potential(gdf)
        analyze_enclave_hole_potential(gdf)
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error analyzing {name}: {e}")
        return False

def analyze_boundary_in_boundary_potential(gdf):
    """Analyze potential for boundary-in-boundary holes"""
    print(f"\n  BOUNDARY-IN-BOUNDARY ANALYSIS:")
    
    try:
        # Identify boundary polygons
        boundary_mask = (gdf['HCV'] == 0)
        if 'ID_feature' in gdf.columns:
            id_boundary_mask = gdf['ID_feature'].astype(str).str.lower().str.startswith('boundary-')
            boundary_mask = boundary_mask | id_boundary_mask
        
        boundary_polygons = gdf[boundary_mask]
        print(f"    Boundary polygons found: {len(boundary_polygons)}")
        
        if len(boundary_polygons) < 2:
            print(f"    ⚠️  Need at least 2 boundary polygons for containment analysis")
            return
        
        # Check for containment relationships
        containment_count = 0
        boundary_polygons = boundary_polygons.copy()
        boundary_polygons['area'] = boundary_polygons.geometry.area
        boundary_polygons = boundary_polygons.sort_values('area', ascending=False)
        
        for i, (outer_idx, outer_row) in enumerate(boundary_polygons.iterrows()):
            outer_geom = outer_row.geometry
            contained_in_this = 0
            
            for j, (inner_idx, inner_row) in enumerate(boundary_polygons.iterrows()):
                if i >= j:  # Skip self and larger polygons
                    continue
                
                inner_geom = inner_row.geometry
                
                try:
                    if outer_geom.contains(inner_geom):
                        contained_in_this += 1
                        containment_count += 1
                except:
                    continue
            
            if contained_in_this > 0:
                blok = outer_row.get('BLOK', f'Feature_{outer_idx}')
                print(f"    • {blok}: contains {contained_in_this} smaller boundary polygon(s)")
        
        print(f"    Total potential boundary-in-boundary holes: {containment_count}")
        
    except Exception as e:
        print(f"    ❌ Error in boundary-in-boundary analysis: {e}")

def analyze_enclave_hole_potential(gdf):
    """Analyze potential for enclave holes"""
    print(f"\n  ENCLAVE HOLE ANALYSIS:")
    
    try:
        boundary_polygons = gdf[gdf['HCV'] == 0]
        enclave_polygons = gdf[gdf['HCV'] == 1]
        
        print(f"    Boundary polygons: {len(boundary_polygons)}")
        print(f"    Enclave polygons: {len(enclave_polygons)}")
        
        if len(boundary_polygons) == 0 or len(enclave_polygons) == 0:
            print(f"    ⚠️  Need both boundary and enclave polygons for hole analysis")
            return
        
        # Check containment by block/subdivision
        total_potential_holes = 0
        blocks_with_holes = 0
        
        for (blok, subdivisi), group in gdf.groupby(['BLOK', 'SUBDIVISI']):
            group_boundaries = group[group['HCV'] == 0]
            group_enclaves = group[group['HCV'] == 1]
            
            if len(group_boundaries) == 0 or len(group_enclaves) == 0:
                continue
            
            group_holes = 0
            for _, boundary_row in group_boundaries.iterrows():
                boundary_geom = boundary_row.geometry
                
                for _, enclave_row in group_enclaves.iterrows():
                    enclave_geom = enclave_row.geometry
                    
                    try:
                        if boundary_geom.contains(enclave_geom):
                            group_holes += 1
                    except:
                        continue
            
            if group_holes > 0:
                print(f"    • {blok}-{subdivisi}: {group_holes} potential enclave hole(s)")
                total_potential_holes += group_holes
                blocks_with_holes += 1
        
        print(f"    Total potential enclave holes: {total_potential_holes}")
        print(f"    Blocks with potential holes: {blocks_with_holes}")
        
    except Exception as e:
        print(f"    ❌ Error in enclave hole analysis: {e}")

def test_synthetic_hole_creation():
    """Test hole creation with synthetic data"""
    print("Creating synthetic test data for hole creation...")
    
    try:
        # Create synthetic polygons
        # Large boundary polygon
        large_boundary = Polygon([(0, 0), (100, 0), (100, 100), (0, 100)])
        
        # Smaller boundary inside large boundary
        small_boundary = Polygon([(20, 20), (40, 20), (40, 40), (20, 40)])
        
        # Enclave inside large boundary
        enclave = Polygon([(60, 60), (80, 60), (80, 80), (60, 80)])
        
        # Create GeoDataFrame
        synthetic_data = {
            'geometry': [large_boundary, small_boundary, enclave],
            'BLOK': ['P01/01', 'P01/01', 'P01/01'],
            'SUBDIVISI': ['SUB A', 'SUB A', 'SUB A'],
            'HCV': [0, 0, 1],
            'ID_feature': ['boundary-large', 'boundary-small', 'enclave-1']
        }
        
        gdf_synthetic = gpd.GeoDataFrame(synthetic_data, crs='EPSG:32748')
        
        print(f"  Created {len(gdf_synthetic)} synthetic polygons")
        
        # Test hole creation logic
        from shapely.geometry import Polygon as ShapelyPolygon
        
        # Test boundary-in-boundary
        large_geom = gdf_synthetic.iloc[0].geometry
        small_geom = gdf_synthetic.iloc[1].geometry
        
        if large_geom.contains(small_geom):
            print(f"  ✓ Large boundary contains small boundary")
            
            # Create hole
            hole_coords = list(small_geom.exterior.coords)
            new_polygon = ShapelyPolygon(large_geom.exterior.coords, [hole_coords])
            
            original_area = large_geom.area
            new_area = new_polygon.area
            hole_area = original_area - new_area
            
            print(f"    Original area: {original_area:,.0f}")
            print(f"    New area with hole: {new_area:,.0f}")
            print(f"    Hole area: {hole_area:,.0f}")
        
        # Test enclave hole
        enclave_geom = gdf_synthetic.iloc[2].geometry
        
        if large_geom.contains(enclave_geom):
            print(f"  ✓ Large boundary contains enclave")
            
            # Create hole for enclave
            enclave_hole_coords = list(enclave_geom.exterior.coords)
            new_polygon_with_enclave = ShapelyPolygon(large_geom.exterior.coords, [enclave_hole_coords])
            
            enclave_area = enclave_geom.area
            net_area = new_polygon_with_enclave.area
            
            print(f"    Enclave area: {enclave_area:,.0f}")
            print(f"    Net plantable area: {net_area:,.0f}")
        
        print(f"  ✓ Synthetic hole creation test completed successfully")
        
    except Exception as e:
        print(f"  ❌ Error in synthetic test: {e}")

def main():
    """Run all hole creation tests"""
    print("Advanced Polygon Hole Creation Test Suite")
    print("=" * 50)
    print("This test analyzes shapefiles for hole creation potential")
    print("and validates the enhanced hole creation logic.")
    print()
    
    test_hole_creation_logic()
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print("• Boundary-in-Boundary holes: Create holes where smaller boundaries are inside larger ones")
    print("• Enclave holes: Create holes where enclave polygons (HCV=1) are inside boundary polygons (HCV=0)")
    print("• Multi-hole support: One polygon can have multiple holes")
    print("• Geometric validation: Uses precise containment checking")
    print("• Area calculation: Net area automatically excludes hole areas")
    print()
    print("Use the enhanced GUI to create holes with these features!")

if __name__ == "__main__":
    main()

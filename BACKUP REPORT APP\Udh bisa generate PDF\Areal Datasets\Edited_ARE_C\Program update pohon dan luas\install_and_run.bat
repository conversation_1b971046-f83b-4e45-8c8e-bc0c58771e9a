@echo off
echo ========================================
echo  TREE COUNTING ANALYSIS - INSTALLATION
echo ========================================
echo.

echo [1/3] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python tidak ditemukan!
    echo Silakan install Python terlebih dahulu dari https://python.org
    pause
    exit /b 1
)
echo Python found ✓
echo.

echo [2/3] Installing required packages...
echo Installing geopandas and dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo.
    echo ERROR: Gagal menginstall dependencies!
    echo Coba jalankan command berikut secara manual:
    echo pip install geopandas pandas shapely fiona pyproj
    pause
    exit /b 1
)
echo Dependencies installed ✓
echo.

echo [3/3] Running tree counting analysis...
echo.
python tree_count_analysis.py
if errorlevel 1 (
    echo.
    echo ERROR: Script gagal dijalankan!
    echo Periksa path file dan error message di atas.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  ANALYSIS COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Check the output file: Polygon_ARE_C_dengan_Jumlah_Pohon.shp
echo.
pause 
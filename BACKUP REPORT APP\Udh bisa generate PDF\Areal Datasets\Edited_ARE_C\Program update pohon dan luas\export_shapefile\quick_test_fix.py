"""
Quick test for geometry fix
"""

import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon, MultiPolygon, GeometryCollection
from shapely.ops import unary_union
import os

def quick_test():
    print("🔧 QUICK GEOMETRY FIX TEST")
    print("=" * 30)
    
    # Test file path
    shapefile_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
    
    if not os.path.exists(shapefile_path):
        print(f"❌ File not found")
        return False
    
    try:
        # Load data
        print("📂 Loading...")
        gdf = gpd.read_file(shapefile_path)
        print(f"✅ Loaded {len(gdf)} features")
        
        # Check geometry types
        geom_types = gdf.geometry.geom_type.value_counts()
        print(f"Geometry types: {dict(geom_types)}")
        
        # Fix problematic geometries
        print("🔧 Fixing geometries...")
        gdf_fixed = gdf.copy()
        fixed_count = 0
        
        for idx, geom in enumerate(gdf_fixed.geometry):
            try:
                if geom.geom_type == 'GeometryCollection':
                    # Extract polygons
                    polygons = [g for g in geom.geoms if g.geom_type in ['Polygon', 'MultiPolygon']]
                    if polygons:
                        if len(polygons) == 1:
                            fixed_geom = polygons[0]
                        else:
                            fixed_geom = unary_union(polygons)
                        
                        if fixed_geom.geom_type == 'MultiPolygon':
                            largest_poly = max(fixed_geom.geoms, key=lambda x: x.area)
                            fixed_geom = largest_poly
                        
                        gdf_fixed.at[idx, 'geometry'] = fixed_geom
                        fixed_count += 1
                
                elif geom.geom_type == 'MultiPolygon':
                    largest_poly = max(geom.geoms, key=lambda x: x.area)
                    gdf_fixed.at[idx, 'geometry'] = largest_poly
                    fixed_count += 1
                
                # Validate
                if not gdf_fixed.at[idx, 'geometry'].is_valid:
                    gdf_fixed.at[idx, 'geometry'] = gdf_fixed.at[idx, 'geometry'].buffer(0)
                    
            except Exception as e:
                print(f"Error fixing geometry {idx}: {e}")
                continue
        
        print(f"✅ Fixed {fixed_count} geometries")
        
        # Test export
        print("💾 Testing export...")
        output_path = "test_fixed_export.shp"
        gdf_fixed.to_file(output_path)
        print(f"✅ Export successful: {output_path}")
        
        # Test hole creation
        print("🕳️  Testing hole creation...")
        holes_created = test_hole_creation(gdf_fixed)
        print(f"✅ Created {holes_created} test holes")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_hole_creation(gdf):
    """Test simple hole creation"""
    try:
        # Standardize columns
        if 'HCV_Catego' in gdf.columns:
            gdf['HCV'] = pd.to_numeric(gdf['HCV_Catego'], errors='coerce').fillna(0).astype(int)
        
        holes_created = 0
        
        # Simple test: find one boundary and one enclave
        boundaries = gdf[gdf['HCV'] == 0]
        enclaves = gdf[gdf['HCV'] == 1]
        
        if len(boundaries) > 0 and len(enclaves) > 0:
            # Test containment
            for _, boundary_row in boundaries.head(3).iterrows():  # Test first 3
                boundary_geom = boundary_row.geometry
                
                for _, enclave_row in enclaves.head(3).iterrows():  # Test first 3
                    enclave_geom = enclave_row.geometry
                    
                    try:
                        if boundary_geom.contains(enclave_geom):
                            # Create hole
                            hole_coords = list(enclave_geom.exterior.coords)
                            new_polygon = Polygon(boundary_geom.exterior.coords, [hole_coords])
                            
                            if new_polygon.is_valid:
                                holes_created += 1
                                print(f"  ✅ Test hole created successfully")
                                break  # Only test one hole
                    except:
                        continue
                
                if holes_created > 0:
                    break
        
        return holes_created
        
    except Exception as e:
        print(f"  ❌ Hole creation test failed: {e}")
        return 0

if __name__ == "__main__":
    success = quick_test()
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The geometry fix should work in the GUI now.")
    else:
        print("\n❌ TESTS FAILED!")

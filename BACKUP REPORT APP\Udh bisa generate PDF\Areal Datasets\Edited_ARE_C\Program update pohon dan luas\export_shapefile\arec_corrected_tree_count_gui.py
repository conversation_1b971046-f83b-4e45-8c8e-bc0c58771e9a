"""
ARE C Corrected Tree Count GUI
GUI yang sudah diperbaiki berdasarkan hasil analisis data:
- HCV = 0 (boundary/batas blok)
- HCV = 1 (enclave/area tidak ditanami)
- 46 blok unik
- Fokus pada total pohon dan luas per blok

Author: Generated for ARE C Analysis (Corrected Version)
Date: 2025-01-03
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import geopandas as gpd
import pandas as pd
import os
import threading

# Default paths
DEFAULT_BOUNDARY_PATH = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Polygon_ARE_C_dengan_Jumlah_Pohon_update_atribut_tabe;.shp"
DEFAULT_POINTS_PATH = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"

class ARECCorrectedTreeCountGUI:
    def __init__(self, root):
        self.root = root
        self.root.title('ARE C Corrected Tree Count & Area Analysis')
        self.root.geometry('1500x900')
        
        # Data
        self.boundary_path = DEFAULT_BOUNDARY_PATH
        self.points_path = DEFAULT_POINTS_PATH
        self.gdf_boundary = None
        self.gdf_points = None
        self.results_df = None
        self.detailed_results_df = None
        self.updated_gdf_boundary = None  # Store updated boundary with attributes
        self.processing = False
        
        self.create_gui()
        self.load_data()

    def create_gui(self):
        """Create GUI interface"""
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # File paths section
        path_frame = ttk.LabelFrame(main_frame, text='File Paths', padding=10)
        path_frame.pack(fill='x', pady=5)
        
        # Boundary file
        ttk.Label(path_frame, text='Boundary Shapefile:').grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.boundary_entry = ttk.Entry(path_frame, width=90)
        self.boundary_entry.grid(row=0, column=1, padx=5, pady=2)
        self.boundary_entry.insert(0, self.boundary_path)
        ttk.Button(path_frame, text='Browse', command=self.browse_boundary).grid(row=0, column=2, padx=5, pady=2)
        
        # Points file
        ttk.Label(path_frame, text='Detection Points:').grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.points_entry = ttk.Entry(path_frame, width=90)
        self.points_entry.grid(row=1, column=1, padx=5, pady=2)
        self.points_entry.insert(0, self.points_path)
        ttk.Button(path_frame, text='Browse', command=self.browse_points).grid(row=1, column=2, padx=5, pady=2)
        
        # Control buttons
        control_frame = ttk.Frame(path_frame)
        control_frame.grid(row=2, column=1, pady=10)
        
        ttk.Button(control_frame, text='Load Data', command=self.start_loading).pack(side='left', padx=5)
        ttk.Button(control_frame, text='Analyze Tree Count & Area', command=self.start_analysis).pack(side='left', padx=5)
        ttk.Button(control_frame, text='Export Results', command=self.export_results).pack(side='left', padx=5)
        ttk.Button(control_frame, text='Save Updated Shapefile', command=self.save_updated_shapefile).pack(side='left', padx=5)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill='both', expand=True, pady=5)
        
        # Tab 1: Summary per Block
        self.create_summary_tab()
        
        # Tab 2: Detailed per Block-HCV
        self.create_detailed_tab()
        
        # Tab 3: Hole Creation
        self.create_hole_tab()
        
        # Tab 4: Log
        self.create_log_tab()

    def create_summary_tab(self):
        """Create summary results tab"""
        summary_frame = ttk.Frame(self.notebook)
        self.notebook.add(summary_frame, text='Summary per Block')
        
        # Results table
        results_frame = ttk.LabelFrame(summary_frame, text='Tree Count & Area Summary per Block', padding=10)
        results_frame.pack(fill='both', expand=True, pady=5)
        
        columns = ('BLOK', 'SUBDIVISI', 'TOTAL_POHON', 'LUAS_BOUNDARY_HA', 'LUAS_ENCLAVE_HA', 
                  'LUAS_NETTO_HA', 'KEPADATAN_POHON_PER_HA', 'FITUR_BOUNDARY', 'FITUR_ENCLAVE')
        self.summary_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=18)
        
        # Configure columns
        column_configs = {
            'BLOK': ('Block', 120),
            'SUBDIVISI': ('Sub Division', 150),
            'TOTAL_POHON': ('Total Trees', 100),
            'LUAS_BOUNDARY_HA': ('Boundary Area (Ha)', 130),
            'LUAS_ENCLAVE_HA': ('Enclave Area (Ha)', 120),
            'LUAS_NETTO_HA': ('Net Area (Ha)', 110),
            'KEPADATAN_POHON_PER_HA': ('Trees/Ha', 90),
            'FITUR_BOUNDARY': ('Boundary Features', 120),
            'FITUR_ENCLAVE': ('Enclave Features', 110)
        }
        
        for col, (header, width) in column_configs.items():
            self.summary_tree.heading(col, text=header)
            self.summary_tree.column(col, width=width, anchor='center')
        
        # Scrollbars
        summary_v_scroll = ttk.Scrollbar(results_frame, orient='vertical', command=self.summary_tree.yview)
        summary_h_scroll = ttk.Scrollbar(results_frame, orient='horizontal', command=self.summary_tree.xview)
        self.summary_tree.configure(yscrollcommand=summary_v_scroll.set, xscrollcommand=summary_h_scroll.set)
        
        self.summary_tree.pack(side='left', fill='both', expand=True)
        summary_v_scroll.pack(side='right', fill='y')
        
        # Summary statistics
        stats_frame = ttk.LabelFrame(summary_frame, text='Overall Statistics', padding=10)
        stats_frame.pack(fill='x', pady=5)
        
        self.stats_text = scrolledtext.ScrolledText(stats_frame, height=8)
        self.stats_text.pack(fill='x')

    def create_detailed_tab(self):
        """Create detailed results tab"""
        detailed_frame = ttk.Frame(self.notebook)
        self.notebook.add(detailed_frame, text='Detailed per Feature')
        
        # Detailed table
        detail_results_frame = ttk.LabelFrame(detailed_frame, text='Detailed Results per Feature', padding=10)
        detail_results_frame.pack(fill='both', expand=True, pady=5)
        
        detail_columns = ('BLOK', 'SUBDIVISI', 'HCV', 'HCV_TYPE', 'POHON_COUNT', 'LUAS_HA', 'ID_FEATURE')
        self.detailed_tree = ttk.Treeview(detail_results_frame, columns=detail_columns, show='headings', height=20)
        
        # Configure detailed columns
        detail_column_configs = {
            'BLOK': ('Block', 120),
            'SUBDIVISI': ('Sub Division', 150),
            'HCV': ('HCV', 50),
            'HCV_TYPE': ('HCV Type', 100),
            'POHON_COUNT': ('Trees', 80),
            'LUAS_HA': ('Area (Ha)', 100),
            'ID_FEATURE': ('Feature ID', 100)
        }
        
        for col, (header, width) in detail_column_configs.items():
            self.detailed_tree.heading(col, text=header)
            self.detailed_tree.column(col, width=width, anchor='center')
        
        # Scrollbars for detailed
        detailed_v_scroll = ttk.Scrollbar(detail_results_frame, orient='vertical', command=self.detailed_tree.yview)
        detailed_h_scroll = ttk.Scrollbar(detail_results_frame, orient='horizontal', command=self.detailed_tree.xview)
        self.detailed_tree.configure(yscrollcommand=detailed_v_scroll.set, xscrollcommand=detailed_h_scroll.set)
        
        self.detailed_tree.pack(side='left', fill='both', expand=True)
        detailed_v_scroll.pack(side='right', fill='y')

    def create_hole_tab(self):
        """Create polygon hole creation tab"""
        hole_frame = ttk.Frame(self.notebook)
        self.notebook.add(hole_frame, text='Polygon Hole Creation')
        
        # Instructions
        instruction_frame = ttk.LabelFrame(hole_frame, text='Hole Creation Instructions', padding=10)
        instruction_frame.pack(fill='x', pady=5)
        
        instruction_text = """
Fitur Pembuatan Hole (Lubang) pada Polygon:

1. Fungsi ini akan membuat lubang pada polygon LSU besar (Boundary/HCV=0) jika terdapat 
   polygon LSU kecil (Enclave/HCV=1) yang berada di dalamnya.

2. Contoh: Jika dalam Blok P09/01 ada boundary polygon yang mengandung enclave polygon 
   di dalamnya, maka boundary polygon akan dibolongi sesuai bentuk enclave.

3. Hasil: Polygon boundary akan memiliki geometri donut (berlubang) yang lebih akurat 
   untuk perhitungan luas netto.

4. Klik 'Create Holes in Boundary Polygons' untuk memulai proses.
        """
        
        instruction_label = tk.Label(instruction_frame, text=instruction_text, justify='left', 
                                   wraplength=800, font=('Arial', 9))
        instruction_label.pack()
        
        # Advanced hole creation controls
        hole_control_frame = ttk.LabelFrame(hole_frame, text='Advanced Hole Creation Controls', padding=10)
        hole_control_frame.pack(fill='x', pady=5)

        # Main enable/disable
        self.create_holes_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(hole_control_frame, text='Enable hole creation during analysis',
                       variable=self.create_holes_var).pack(anchor='w', pady=5)

        # Specific hole creation options
        options_subframe = ttk.Frame(hole_control_frame)
        options_subframe.pack(fill='x', pady=5)

        # Boundary-in-Boundary holes
        self.boundary_holes_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_subframe, text='Create Boundary-in-Boundary holes (large boundary contains smaller boundary)',
                       variable=self.boundary_holes_var).pack(anchor='w', pady=2)

        # Enclave holes
        self.enclave_holes_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_subframe, text='Create Enclave holes (boundary contains enclave polygons)',
                       variable=self.enclave_holes_var).pack(anchor='w', pady=2)

        # Multi-hole support
        self.multi_holes_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_subframe, text='Enable multi-hole support (one polygon can have multiple holes)',
                       variable=self.multi_holes_var).pack(anchor='w', pady=2)

        # Control buttons
        button_frame = ttk.Frame(hole_control_frame)
        button_frame.pack(fill='x', pady=5)

        ttk.Button(button_frame, text='Advanced Hole Creation',
                  command=self.create_advanced_polygon_holes).pack(side='left', padx=5)

        ttk.Button(button_frame, text='Preview Hole Analysis',
                  command=self.preview_advanced_holes).pack(side='left', padx=5)

        ttk.Button(button_frame, text='Load Alternative Shapefile',
                  command=self.load_alternative_shapefile).pack(side='left', padx=5)

        ttk.Button(button_frame, text='🚀 COMPREHENSIVE ANALYSIS',
                  command=self.run_comprehensive_analysis).pack(side='left', padx=5)
        
        # Hole creation results
        hole_results_frame = ttk.LabelFrame(hole_frame, text='Hole Creation Results', padding=10)
        hole_results_frame.pack(fill='both', expand=True, pady=5)
        
        self.hole_results_text = scrolledtext.ScrolledText(hole_results_frame, height=15)
        self.hole_results_text.pack(fill='both', expand=True)

    def create_log_tab(self):
        """Create log tab"""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text='Processing Log')
        
        # Log section
        log_display_frame = ttk.LabelFrame(log_frame, text='Processing Log', padding=10)
        log_display_frame.pack(fill='both', expand=True, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_display_frame, height=25, state='disabled')
        self.log_text.pack(fill='both', expand=True)
        
        # Status
        self.status_var = tk.StringVar(value='Ready')
        ttk.Label(log_frame, textvariable=self.status_var).pack(pady=5)

    def log(self, message):
        """Add message to log"""
        self.log_text.configure(state='normal')
        self.log_text.insert('end', message + '\n')
        self.log_text.see('end')
        self.log_text.configure(state='disabled')
        print(message)  # Also print to console

    def browse_boundary(self):
        """Browse for boundary file"""
        path = filedialog.askopenfilename(filetypes=[('Shapefile', '*.shp')])
        if path:
            self.boundary_path = path
            self.boundary_entry.delete(0, 'end')
            self.boundary_entry.insert(0, path)

    def browse_points(self):
        """Browse for points file"""
        path = filedialog.askopenfilename(filetypes=[('Shapefile', '*.shp')])
        if path:
            self.points_path = path
            self.points_entry.delete(0, 'end')
            self.points_entry.insert(0, path)

    def start_loading(self):
        """Start loading data in separate thread"""
        if self.processing:
            return

        self.processing = True
        thread = threading.Thread(target=self.load_data)
        thread.daemon = True
        thread.start()

    def load_data(self):
        """Load shapefile data with optimization"""
        self.log("Loading data...")
        self.status_var.set('Loading data...')

        try:
            # Get paths from entries
            self.boundary_path = self.boundary_entry.get()
            self.points_path = self.points_entry.get()

            # Check files exist
            if not os.path.exists(self.boundary_path):
                self.log(f"ERROR: Boundary file not found: {self.boundary_path}")
                return

            if not os.path.exists(self.points_path):
                self.log(f"ERROR: Points file not found: {self.points_path}")
                return

            # Load boundary (fast)
            self.log("Loading boundary polygons...")
            self.gdf_boundary = gpd.read_file(self.boundary_path)
            self.log(f"✓ Loaded {len(self.gdf_boundary)} boundary polygons")

            # Standardize column names first (fast operation)
            column_mappings = {
                'SUB_DIVISI': 'SUBDIVISI',
                'HCV_Catego': 'HCV',
                'Jumlah_Poh': 'JUMLAH_POH'
            }

            for old, new in column_mappings.items():
                if old in self.gdf_boundary.columns and new not in self.gdf_boundary.columns:
                    self.gdf_boundary = self.gdf_boundary.rename(columns={old: new})
                    self.log(f"Renamed column: {old} → {new}")

            # Clean data types - HCV should be numeric
            if 'HCV' in self.gdf_boundary.columns:
                self.gdf_boundary['HCV'] = pd.to_numeric(self.gdf_boundary['HCV'], errors='coerce').fillna(0).astype(int)

            # Load points with progress indication
            self.log("Loading detection points... (this may take 10-30 seconds)")
            self.status_var.set('Loading 243,021 detection points...')
            self.root.update_idletasks()  # Update GUI

            # Load points (this is the slow part)
            self.gdf_points = gpd.read_file(self.points_path)
            self.log(f"✓ Loaded {len(self.gdf_points)} detection points")
            
            # Show data info
            self.log(f"Boundary CRS: {self.gdf_boundary.crs}")
            self.log(f"Points CRS: {self.gdf_points.crs}")
            
            # Analyze data structure
            if 'BLOK' in self.gdf_boundary.columns:
                unique_blocks = sorted(self.gdf_boundary['BLOK'].unique())
                self.log(f"Found {len(unique_blocks)} unique blocks")
                
                # Show HCV distribution
                if 'HCV' in self.gdf_boundary.columns:
                    hcv_counts = self.gdf_boundary['HCV'].value_counts().sort_index()
                    self.log(f"HCV distribution: {dict(hcv_counts)}")
                    self.log("HCV 0 = Boundary polygons, HCV 1 = Enclave polygons")
            
            self.status_var.set('Data loaded successfully')

        except Exception as e:
            error_msg = f"Error loading data: {str(e)}"
            self.log(error_msg)
            self.status_var.set('Error loading data')
            messagebox.showerror("Error", error_msg)

        finally:
            self.processing = False

    def load_alternative_shapefile(self):
        """Load alternative shapefile for hole creation analysis"""
        alt_path = filedialog.askopenfilename(
            title="Select Alternative Shapefile for Hole Creation",
            filetypes=[('Shapefile', '*.shp'), ('All files', '*.*')],
            initialdir=os.path.dirname(self.boundary_path) if self.boundary_path else None
        )

        if alt_path:
            try:
                self.log(f"Loading alternative shapefile: {os.path.basename(alt_path)}")
                alt_gdf = gpd.read_file(alt_path)

                # Standardize column names
                column_mappings = {
                    'SUB_DIVISI': 'SUBDIVISI',
                    'HCV_Catego': 'HCV',
                    'Jumlah_Poh': 'JUMLAH_POH'
                }

                for old, new in column_mappings.items():
                    if old in alt_gdf.columns and new not in alt_gdf.columns:
                        alt_gdf = alt_gdf.rename(columns={old: new})

                # Clean data types
                if 'HCV' in alt_gdf.columns:
                    alt_gdf['HCV'] = pd.to_numeric(alt_gdf['HCV'], errors='coerce').fillna(0).astype(int)

                # Replace current boundary data
                self.gdf_boundary = alt_gdf
                self.log(f"✓ Alternative shapefile loaded: {len(alt_gdf)} features")
                self.log(f"  Columns: {list(alt_gdf.columns)}")

                # Update path display
                self.boundary_entry.delete(0, 'end')
                self.boundary_entry.insert(0, alt_path)
                self.boundary_path = alt_path

                # Show data info
                if 'HCV' in alt_gdf.columns:
                    hcv_counts = alt_gdf['HCV'].value_counts().sort_index()
                    self.log(f"  HCV distribution: {dict(hcv_counts)}")

                messagebox.showinfo("Success", f"Alternative shapefile loaded successfully!\n\n"
                                              f"Features: {len(alt_gdf)}\n"
                                              f"File: {os.path.basename(alt_path)}")

            except Exception as e:
                error_msg = f"Error loading alternative shapefile: {str(e)}"
                self.log(error_msg)
                messagebox.showerror("Error", error_msg)

    def start_analysis(self):
        """Start analysis in separate thread"""
        if self.processing:
            return
        
        if self.gdf_boundary is None or self.gdf_points is None:
            messagebox.showerror("Error", "Please load data first!")
            return
        
        self.processing = True
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()

    def run_analysis(self):
        """Run the tree counting and area analysis"""
        try:
            self.status_var.set('Running analysis...')
            self.log("=== STARTING CORRECTED TREE COUNT & AREA ANALYSIS ===")
            
            # Copy data
            gdf_boundary = self.gdf_boundary.copy()
            gdf_points = self.gdf_points.copy()
            
            # Ensure CRS compatibility
            if gdf_boundary.crs != gdf_points.crs:
                self.log(f"Converting points CRS: {gdf_points.crs} → {gdf_boundary.crs}")
                gdf_points = gdf_points.to_crs(gdf_boundary.crs)
            
            # Convert to projected CRS for area calculation
            if not gdf_boundary.crs or not gdf_boundary.crs.is_projected:
                self.log("Converting to projected CRS for area calculation...")
                gdf_boundary = gdf_boundary.to_crs(epsg=32748)  # UTM Zone 48S
                gdf_points = gdf_points.to_crs(epsg=32748)
            
            # Calculate area for all polygons
            self.log("Calculating polygon areas...")
            gdf_boundary['area_sqm'] = gdf_boundary.geometry.area
            gdf_boundary['area_ha'] = gdf_boundary['area_sqm'] / 10000
            
            # Perform spatial overlay - ONLY with boundary polygons (HCV = 0)
            self.log("Filtering boundary polygons (HCV = 0) for tree counting...")
            boundary_polygons = gdf_boundary[gdf_boundary['HCV'] == 0].copy()
            self.log(f"Found {len(boundary_polygons)} boundary polygons for tree counting")
            
            if len(boundary_polygons) == 0:
                self.log("ERROR: No boundary polygons (HCV=0) found!")
                return
            
            # Spatial overlay
            self.log("Performing spatial overlay (point-in-polygon) with boundary polygons...")
            overlay_result = gpd.sjoin(gdf_points, boundary_polygons, how='inner', predicate='within')
            self.log(f"Found {len(overlay_result)} points within boundary polygons")
            
            # Process results by BLOK and SUBDIVISI
            self.log("Processing results by Block and Sub Division...")
            
            # Create detailed results first
            detailed_results = []
            
            for _, row in gdf_boundary.iterrows():
                blok = row['BLOK']
                subdivisi = row['SUBDIVISI']
                hcv = row['HCV']
                hcv_type = 'Boundary' if hcv == 0 else 'Enclave'
                area_ha = row['area_ha']
                feature_id = row.get('ID_Feature', f"Feature_{row.name}")
                
                # Count trees for this specific feature (only if it's boundary)
                if hcv == 0:
                    feature_trees = len(overlay_result[overlay_result.index_right == row.name])
                else:
                    feature_trees = 0  # Enclaves don't count trees
                
                detailed_results.append({
                    'BLOK': blok,
                    'SUBDIVISI': subdivisi,
                    'HCV': hcv,
                    'HCV_TYPE': hcv_type,
                    'POHON_COUNT': feature_trees,
                    'LUAS_HA': area_ha,
                    'ID_FEATURE': feature_id
                })
            
            self.detailed_results_df = pd.DataFrame(detailed_results)
            
            # Create summary by BLOK and SUBDIVISI
            summary_results = []
            
            for (blok, subdivisi), group in gdf_boundary.groupby(['BLOK', 'SUBDIVISI']):
                # Separate boundary and enclave features
                boundary_features = group[group['HCV'] == 0]
                enclave_features = group[group['HCV'] == 1]
                
                # Calculate areas
                boundary_area = boundary_features['area_ha'].sum()
                enclave_area = enclave_features['area_ha'].sum()
                net_area = boundary_area - enclave_area
                
                # Count trees (only from overlay result for this blok-subdivisi)
                blok_subdivisi_trees = 0
                for _, boundary_row in boundary_features.iterrows():
                    trees_in_feature = len(overlay_result[overlay_result.index_right == boundary_row.name])
                    blok_subdivisi_trees += trees_in_feature
                
                # Calculate density
                tree_density = blok_subdivisi_trees / net_area if net_area > 0 else 0
                
                summary_results.append({
                    'BLOK': blok,
                    'SUBDIVISI': subdivisi,
                    'TOTAL_POHON': blok_subdivisi_trees,
                    'LUAS_BOUNDARY_HA': boundary_area,
                    'LUAS_ENCLAVE_HA': enclave_area,
                    'LUAS_NETTO_HA': net_area,
                    'KEPADATAN_POHON_PER_HA': tree_density,
                    'FITUR_BOUNDARY': len(boundary_features),
                    'FITUR_ENCLAVE': len(enclave_features)
                })
                
                self.log(f"  {blok} - {subdivisi}: {blok_subdivisi_trees:,} trees, "
                       f"{boundary_area:.2f}ha boundary, {enclave_area:.2f}ha enclave, "
                       f"{net_area:.2f}ha net, {tree_density:.2f} trees/ha")
            
            # Store results
            self.results_df = pd.DataFrame(summary_results)
            self.results_df = self.results_df.sort_values(['BLOK', 'SUBDIVISI'])
            
            # Update boundary shapefile with results
            self.update_boundary_attributes(gdf_boundary)
            
            # Create holes if enabled
            if self.create_holes_var.get():
                self.log("Creating advanced polygon holes...")
                gdf_boundary = self.perform_advanced_hole_creation(gdf_boundary)
            
            # Store updated boundary
            self.updated_gdf_boundary = gdf_boundary
            
            # Update GUI
            self.update_summary_table()
            self.update_detailed_table()
            self.update_statistics()
            
            # Log overall results
            total_trees = self.results_df['TOTAL_POHON'].sum()
            total_boundary_area = self.results_df['LUAS_BOUNDARY_HA'].sum()
            total_enclave_area = self.results_df['LUAS_ENCLAVE_HA'].sum()
            total_net_area = self.results_df['LUAS_NETTO_HA'].sum()
            overall_density = total_trees / total_net_area if total_net_area > 0 else 0
            
            self.log(f"\nOVERALL RESULTS:")
            self.log(f"  Total trees detected: {total_trees:,}")
            self.log(f"  Total boundary area: {total_boundary_area:,.2f} ha")
            self.log(f"  Total enclave area: {total_enclave_area:,.2f} ha")
            self.log(f"  Total net plantable area: {total_net_area:,.2f} ha")
            self.log(f"  Overall tree density: {overall_density:.2f} trees/ha")
            self.log(f"  Number of block-subdivisions: {len(self.results_df)}")
            
            self.status_var.set('Analysis completed successfully')
            self.log("=== ANALYSIS COMPLETED ===")
            
        except Exception as e:
            error_msg = f"Error during analysis: {str(e)}"
            self.log(error_msg)
            self.status_var.set('Analysis failed')
            import traceback
            self.log(traceback.format_exc())
            
        finally:
            self.processing = False

    def update_summary_table(self):
        """Update the summary results table"""
        # Clear existing data
        for item in self.summary_tree.get_children():
            self.summary_tree.delete(item)
        
        if self.results_df is not None:
            for _, row in self.results_df.iterrows():
                values = (
                    row['BLOK'],
                    row['SUBDIVISI'],
                    f"{row['TOTAL_POHON']:,}",
                    f"{row['LUAS_BOUNDARY_HA']:.2f}",
                    f"{row['LUAS_ENCLAVE_HA']:.2f}",
                    f"{row['LUAS_NETTO_HA']:.2f}",
                    f"{row['KEPADATAN_POHON_PER_HA']:.2f}",
                    int(row['FITUR_BOUNDARY']),
                    int(row['FITUR_ENCLAVE'])
                )
                self.summary_tree.insert('', 'end', values=values)

    def update_detailed_table(self):
        """Update the detailed results table"""
        # Clear existing data
        for item in self.detailed_tree.get_children():
            self.detailed_tree.delete(item)
        
        if self.detailed_results_df is not None:
            for _, row in self.detailed_results_df.iterrows():
                values = (
                    row['BLOK'],
                    row['SUBDIVISI'],
                    row['HCV'],
                    row['HCV_TYPE'],
                    f"{row['POHON_COUNT']:,}",
                    f"{row['LUAS_HA']:.2f}",
                    row['ID_FEATURE']
                )
                self.detailed_tree.insert('', 'end', values=values)

    def update_statistics(self):
        """Update summary statistics"""
        if self.results_df is None:
            return
        
        # Calculate summary statistics
        total_trees = self.results_df['TOTAL_POHON'].sum()
        total_boundary_area = self.results_df['LUAS_BOUNDARY_HA'].sum()
        total_enclave_area = self.results_df['LUAS_ENCLAVE_HA'].sum()
        total_net_area = self.results_df['LUAS_NETTO_HA'].sum()
        overall_density = total_trees / total_net_area if total_net_area > 0 else 0
        
        num_blocks = len(self.results_df)
        avg_trees_per_block = self.results_df['TOTAL_POHON'].mean()
        avg_net_area_per_block = self.results_df['LUAS_NETTO_HA'].mean()
        
        # Blocks with highest/lowest tree counts
        max_trees_idx = self.results_df['TOTAL_POHON'].idxmax()
        min_trees_idx = self.results_df['TOTAL_POHON'].idxmin()
        max_trees_block = self.results_df.loc[max_trees_idx]
        min_trees_block = self.results_df.loc[min_trees_idx]
        
        # Blocks with highest/lowest density
        max_density_idx = self.results_df['KEPADATAN_POHON_PER_HA'].idxmax()
        min_density_idx = self.results_df['KEPADATAN_POHON_PER_HA'].idxmin()
        max_density_block = self.results_df.loc[max_density_idx]
        min_density_block = self.results_df.loc[min_density_idx]
        
        summary_text = f"""=== COMPREHENSIVE ANALYSIS STATISTICS ===

OVERALL TOTALS:
• Total Trees Detected: {total_trees:,}
• Total Boundary Area: {total_boundary_area:,.2f} hectares
• Total Enclave Area: {total_enclave_area:,.2f} hectares  
• Total Net Plantable Area: {total_net_area:,.2f} hectares
• Overall Tree Density: {overall_density:.2f} trees/hectare
• Number of Block-SubDivisions: {num_blocks}

AVERAGES:
• Average Trees per Block-SubDiv: {avg_trees_per_block:.0f}
• Average Net Area per Block-SubDiv: {avg_net_area_per_block:.2f} ha

HIGHEST/LOWEST TREE COUNTS:
• Highest: {max_trees_block['BLOK']} - {max_trees_block['SUBDIVISI']} ({max_trees_block['TOTAL_POHON']:,} trees)
• Lowest: {min_trees_block['BLOK']} - {min_trees_block['SUBDIVISI']} ({min_trees_block['TOTAL_POHON']:,} trees)

HIGHEST/LOWEST TREE DENSITY:
• Highest: {max_density_block['BLOK']} - {max_density_block['SUBDIVISI']} ({max_density_block['KEPADATAN_POHON_PER_HA']:.2f} trees/ha)
• Lowest: {min_density_block['BLOK']} - {min_density_block['SUBDIVISI']} ({min_density_block['KEPADATAN_POHON_PER_HA']:.2f} trees/ha)

AREA EFFICIENCY:
• Enclave Percentage: {(total_enclave_area/total_boundary_area)*100:.1f}% of boundary area
• Net Plantable Percentage: {(total_net_area/total_boundary_area)*100:.1f}% of boundary area
"""
        
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, summary_text)

    def export_results(self):
        """Export results to Excel with multiple sheets"""
        if self.results_df is None or self.detailed_results_df is None:
            messagebox.showerror("Error", "No results to export!")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension='.xlsx',
            filetypes=[('Excel files', '*.xlsx'), ('All files', '*.*')]
        )
        
        if filename:
            try:
                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    # Summary sheet
                    self.results_df.to_excel(writer, sheet_name='Summary_per_Block', index=False)
                    
                    # Detailed sheet
                    self.detailed_results_df.to_excel(writer, sheet_name='Detailed_per_Feature', index=False)
                    
                    # Statistics sheet
                    stats_data = {
                        'Metric': ['Total Trees', 'Total Boundary Area (Ha)', 'Total Enclave Area (Ha)', 
                                  'Total Net Area (Ha)', 'Overall Density (trees/ha)', 'Number of Blocks'],
                        'Value': [
                            self.results_df['TOTAL_POHON'].sum(),
                            self.results_df['LUAS_BOUNDARY_HA'].sum(),
                            self.results_df['LUAS_ENCLAVE_HA'].sum(),
                            self.results_df['LUAS_NETTO_HA'].sum(),
                            self.results_df['TOTAL_POHON'].sum() / self.results_df['LUAS_NETTO_HA'].sum(),
                            len(self.results_df)
                        ]
                    }
                    stats_df = pd.DataFrame(stats_data)
                    stats_df.to_excel(writer, sheet_name='Overall_Statistics', index=False)
                
                self.log(f"Results exported to Excel: {filename}")
                messagebox.showinfo("Success", f"Results exported to:\n{filename}")
                
            except Exception as e:
                error_msg = f"Export failed: {str(e)}"
                self.log(error_msg)
                messagebox.showerror("Error", error_msg)

    def update_boundary_attributes(self, gdf_boundary):
        """Update boundary shapefile attributes with analysis results"""
        self.log("Updating boundary shapefile attributes...")
        
        try:
            # Add/update columns if they don't exist
            required_columns = ['luas_total', 'luas_enclave', 'luas_netto', 'jumlah_pohon_updated']
            
            for col in required_columns:
                if col not in gdf_boundary.columns:
                    gdf_boundary[col] = 0.0
            
            # Update attributes based on analysis results
            if self.results_df is not None:
                for _, result_row in self.results_df.iterrows():
                    blok = result_row['BLOK']
                    subdivisi = result_row['SUBDIVISI']
                    
                    # Update boundary features (HCV=0)
                    boundary_mask = (gdf_boundary['BLOK'] == blok) & \
                                   (gdf_boundary['SUBDIVISI'] == subdivisi) & \
                                   (gdf_boundary['HCV'] == 0)
                    
                    if boundary_mask.any():
                        gdf_boundary.loc[boundary_mask, 'luas_total'] = result_row['LUAS_BOUNDARY_HA']
                        gdf_boundary.loc[boundary_mask, 'luas_enclave'] = result_row['LUAS_ENCLAVE_HA']
                        gdf_boundary.loc[boundary_mask, 'luas_netto'] = result_row['LUAS_NETTO_HA']
                        gdf_boundary.loc[boundary_mask, 'jumlah_pohon_updated'] = result_row['TOTAL_POHON']
                    
                    # Update enclave features (HCV=1) - no trees but area info
                    enclave_mask = (gdf_boundary['BLOK'] == blok) & \
                                  (gdf_boundary['SUBDIVISI'] == subdivisi) & \
                                  (gdf_boundary['HCV'] == 1)
                    
                    if enclave_mask.any():
                        gdf_boundary.loc[enclave_mask, 'luas_total'] = 0  # Enclaves don't count as total area
                        gdf_boundary.loc[enclave_mask, 'luas_enclave'] = gdf_boundary.loc[enclave_mask, 'area_ha']
                        gdf_boundary.loc[enclave_mask, 'luas_netto'] = 0  # Enclaves are not plantable
                        gdf_boundary.loc[enclave_mask, 'jumlah_pohon_updated'] = 0  # No trees in enclaves
            
            self.log("✓ Boundary attributes updated successfully")
            
        except Exception as e:
            self.log(f"Error updating boundary attributes: {str(e)}")

    def perform_advanced_hole_creation(self, gdf_boundary):
        """Perform advanced polygon hole creation with multiple strategies"""
        self.log("=== STARTING ADVANCED HOLE CREATION ===")

        try:
            from shapely.geometry import Polygon, MultiPolygon
            from shapely.ops import unary_union
            
            # First, validate and repair geometries
            self.log("Validating and repairing geometries...")
            gdf_result = self.validate_and_repair_geometries(gdf_boundary.copy())
            total_holes_created = 0

            # Strategy 1: Boundary-in-Boundary holes
            if self.boundary_holes_var.get():
                self.log("Strategy 1: Creating Boundary-in-Boundary holes...")
                boundary_holes = self.create_boundary_in_boundary_holes(gdf_result)
                total_holes_created += boundary_holes

            # Strategy 2: Enclave holes
            if self.enclave_holes_var.get():
                self.log("Strategy 2: Creating Enclave holes...")
                enclave_holes = self.create_enclave_holes(gdf_result)
                total_holes_created += enclave_holes

            self.log(f"✓ Advanced hole creation completed. Total holes created: {total_holes_created}")
            return gdf_result

        except Exception as e:
            self.log(f"Error during advanced hole creation: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return gdf_boundary

    def validate_and_repair_geometries(self, gdf):
        """Validate and repair invalid geometries"""
        from shapely.geometry import Polygon, MultiPolygon
        from shapely.validation import make_valid
        
        repaired_count = 0
        invalid_count = 0
        
        for idx, row in gdf.iterrows():
            geom = row.geometry
            
            if geom is None:
                self.log(f"    Warning: Null geometry found for feature {idx}")
                invalid_count += 1
                continue
            
            # Check if geometry is valid
            if not geom.is_valid:
                try:
                    # Try to repair the geometry
                    repaired_geom = make_valid(geom)
                    if repaired_geom is not None and repaired_geom.is_valid:
                        gdf.at[idx, 'geometry'] = repaired_geom
                        repaired_count += 1
                    else:
                        self.log(f"    Warning: Could not repair geometry for feature {idx}")
                        invalid_count += 1
                except Exception as repair_error:
                    self.log(f"    Warning: Error repairing geometry for feature {idx}: {repair_error}")
                    invalid_count += 1
        
        if repaired_count > 0:
            self.log(f"  ✓ Repaired {repaired_count} invalid geometries")
        if invalid_count > 0:
            self.log(f"  ⚠ Found {invalid_count} geometries that could not be repaired")
        
        return gdf

    def create_boundary_in_boundary_holes(self, gdf):
        """Create holes where smaller boundary polygons are contained within larger boundary polygons"""
        from shapely.geometry import Polygon, MultiPolygon
        holes_created = 0

        try:
            # Identify boundary polygons (HCV=0 OR ID_feature starts with "boundary-")
            boundary_mask = (gdf['HCV'] == 0)
            if 'ID_feature' in gdf.columns:
                id_boundary_mask = gdf['ID_feature'].astype(str).str.lower().str.startswith('boundary-')
                boundary_mask = boundary_mask | id_boundary_mask

            boundary_polygons = gdf[boundary_mask].copy()
            self.log(f"  Found {len(boundary_polygons)} boundary polygons for analysis")

            if len(boundary_polygons) < 2:
                self.log("  Not enough boundary polygons for boundary-in-boundary analysis")
                return 0

            # Filter out invalid geometries
            valid_boundaries = []
            for idx, row in boundary_polygons.iterrows():
                geom = row.geometry
                if geom is not None and hasattr(geom, 'is_valid') and geom.is_valid:
                    valid_boundaries.append((idx, row))
                else:
                    self.log(f"    Skipping invalid geometry for {row.get('BLOK', 'Unknown')}")

            if len(valid_boundaries) < 2:
                self.log("  Not enough valid boundary polygons for analysis")
                return 0

            # Sort by area (largest first) to process containers before contained
            valid_boundaries.sort(key=lambda x: x[1].geometry.area, reverse=True)

            processed_indices = set()

            for i, (outer_idx, outer_row) in enumerate(valid_boundaries):
                if outer_idx in processed_indices:
                    continue

                outer_geom = outer_row.geometry
                contained_polygons = []

                # Check all smaller polygons
                for j, (inner_idx, inner_row) in enumerate(valid_boundaries):
                    if i >= j or inner_idx in processed_indices:  # Skip self and larger polygons
                        continue

                    inner_geom = inner_row.geometry

                    # Check if inner polygon is completely contained within outer polygon
                    try:
                        if self.check_containment_with_tolerance(outer_geom, inner_geom):
                            contained_polygons.append(inner_geom)
                            processed_indices.add(inner_idx)
                            self.log(f"    Found contained boundary: {inner_row.get('BLOK', 'Unknown')} within {outer_row.get('BLOK', 'Unknown')}")
                    except Exception as geom_error:
                        self.log(f"    Geometry error checking containment: {geom_error}")
                        continue

                # Create holes if we found contained polygons
                if contained_polygons and self.multi_holes_var.get():
                    try:
                        # Create polygon with multiple holes
                        holes = []
                        for contained_geom in contained_polygons:
                            # Handle both Polygon and MultiPolygon
                            if isinstance(contained_geom, Polygon):
                                holes.append(list(contained_geom.exterior.coords))
                            elif isinstance(contained_geom, MultiPolygon):
                                # For MultiPolygon, use the largest polygon as the hole
                                largest_poly = max(contained_geom.geoms, key=lambda p: p.area)
                                holes.append(list(largest_poly.exterior.coords))

                        if holes:
                            # Handle outer geometry type
                            if isinstance(outer_geom, Polygon):
                                new_polygon = Polygon(outer_geom.exterior.coords, holes)
                                gdf.at[outer_idx, 'geometry'] = new_polygon
                                holes_created += len(holes)
                                self.log(f"    ✓ Created {len(holes)} holes in boundary polygon {outer_row.get('BLOK', outer_idx)}")
                            elif isinstance(outer_geom, MultiPolygon):
                                # For MultiPolygon, only create holes in the largest polygon
                                largest_outer = max(outer_geom.geoms, key=lambda p: p.area)
                                new_polygon = Polygon(largest_outer.exterior.coords, holes)
                                # Replace the entire MultiPolygon with the new holed polygon
                                gdf.at[outer_idx, 'geometry'] = new_polygon
                                holes_created += len(holes)
                                self.log(f"    ✓ Created {len(holes)} holes in boundary MultiPolygon {outer_row.get('BLOK', outer_idx)}")

                    except Exception as hole_error:
                        self.log(f"    Error creating boundary holes: {hole_error}")

            self.log(f"  Boundary-in-Boundary holes created: {holes_created}")
            return holes_created

        except Exception as e:
            self.log(f"Error in boundary-in-boundary hole creation: {str(e)}")
            return 0

    def check_containment_with_tolerance(self, outer_geom, inner_geom, buffer_tolerance=0.1):
        """Check if inner geometry is contained within outer geometry with tolerance"""
        try:
            # First try exact containment
            if outer_geom.contains(inner_geom):
                return True
            
            # If not exactly contained, try with a small buffer
            try:
                buffered_outer = outer_geom.buffer(buffer_tolerance)
                if buffered_outer.contains(inner_geom):
                    return True
            except:
                pass
            
            # Check if inner geometry is mostly within outer (90% overlap)
            try:
                intersection = outer_geom.intersection(inner_geom)
                if intersection.area / inner_geom.area > 0.9:
                    return True
            except:
                pass
            
            return False
        except Exception:
            return False

    def create_enclave_holes(self, gdf):
        """Create holes in boundary polygons for contained enclave polygons"""
        from shapely.geometry import Polygon, MultiPolygon
        holes_created = 0

        try:
            # Identify boundary and enclave polygons
            boundary_polygons = gdf[gdf['HCV'] == 0].copy()
            enclave_polygons = gdf[gdf['HCV'] == 1].copy()

            self.log(f"  Found {len(boundary_polygons)} boundary and {len(enclave_polygons)} enclave polygons")

            if len(boundary_polygons) == 0 or len(enclave_polygons) == 0:
                self.log("  No boundary or enclave polygons found for enclave hole creation")
                return 0

            # Group by BLOK and SUBDIVISI for efficient processing
            for (blok, subdivisi), group in gdf.groupby(['BLOK', 'SUBDIVISI']):
                group_boundaries = group[group['HCV'] == 0]
                group_enclaves = group[group['HCV'] == 1]

                if len(group_boundaries) == 0 or len(group_enclaves) == 0:
                    continue

                self.log(f"  Processing {blok}-{subdivisi}: {len(group_boundaries)} boundaries, {len(group_enclaves)} enclaves")

                # For each boundary in this group
                for boundary_idx, boundary_row in group_boundaries.iterrows():
                    boundary_geom = boundary_row.geometry
                    
                    # Skip invalid geometries
                    if boundary_geom is None or not hasattr(boundary_geom, 'is_valid') or not boundary_geom.is_valid:
                        self.log(f"    Skipping invalid boundary geometry for {blok}-{subdivisi}")
                        continue

                    contained_enclaves = []

                    # Check which enclaves are contained in this boundary
                    for _, enclave_row in group_enclaves.iterrows():
                        enclave_geom = enclave_row.geometry
                        
                        # Skip invalid geometries
                        if enclave_geom is None or not hasattr(enclave_geom, 'is_valid') or not enclave_geom.is_valid:
                            continue

                        try:
                            if self.check_containment_with_tolerance(boundary_geom, enclave_geom):
                                contained_enclaves.append(enclave_geom)
                        except Exception as geom_error:
                            self.log(f"    Geometry error checking enclave containment: {geom_error}")
                            continue

                    # Create holes for contained enclaves
                    if contained_enclaves:
                        try:
                            holes = []
                            for enclave_geom in contained_enclaves:
                                # Handle both Polygon and MultiPolygon enclaves
                                if isinstance(enclave_geom, Polygon):
                                    holes.append(list(enclave_geom.exterior.coords))
                                elif isinstance(enclave_geom, MultiPolygon):
                                    # For MultiPolygon enclaves, create holes for all polygons
                                    for poly in enclave_geom.geoms:
                                        if isinstance(poly, Polygon):
                                            holes.append(list(poly.exterior.coords))

                            if holes:
                                # Handle boundary geometry type
                                if isinstance(boundary_geom, Polygon):
                                    new_polygon = Polygon(boundary_geom.exterior.coords, holes)
                                    gdf.at[boundary_idx, 'geometry'] = new_polygon
                                    holes_created += len(holes)
                                    self.log(f"    ✓ Created {len(holes)} enclave holes in {blok}-{subdivisi}")
                                elif isinstance(boundary_geom, MultiPolygon):
                                    # For MultiPolygon boundary, create holes in the largest polygon
                                    largest_boundary = max(boundary_geom.geoms, key=lambda p: p.area)
                                    new_polygon = Polygon(largest_boundary.exterior.coords, holes)
                                    # Replace the entire MultiPolygon with the new holed polygon
                                    gdf.at[boundary_idx, 'geometry'] = new_polygon
                                    holes_created += len(holes)
                                    self.log(f"    ✓ Created {len(holes)} enclave holes in MultiPolygon {blok}-{subdivisi}")

                        except Exception as hole_error:
                            self.log(f"    Error creating enclave holes for {blok}-{subdivisi}: {hole_error}")

            self.log(f"  Enclave holes created: {holes_created}")
            return holes_created

        except Exception as e:
            self.log(f"Error in enclave hole creation: {str(e)}")
            return 0

    def perform_hole_creation(self, gdf_boundary):
        """Perform polygon hole creation for boundary polygons containing enclaves"""
        self.log("Performing polygon hole creation...")
        
        try:
            from shapely.geometry import Polygon, MultiPolygon
            from shapely.ops import unary_union
            
            gdf_result = gdf_boundary.copy()
            holes_created = 0
            
            # Group by BLOK and SUBDIVISI to find boundary-enclave relationships
            for (blok, subdivisi), group in gdf_result.groupby(['BLOK', 'SUBDIVISI']):
                boundary_features = group[group['HCV'] == 0]
                enclave_features = group[group['HCV'] == 1]
                
                if len(boundary_features) == 0 or len(enclave_features) == 0:
                    continue
                
                self.log(f"Processing holes for {blok} - {subdivisi}...")
                
                # For each boundary polygon, check if it contains enclaves
                for boundary_idx, boundary_row in boundary_features.iterrows():
                    boundary_geom = boundary_row.geometry
                    
                    # Find enclaves that are completely within this boundary
                    contained_enclaves = []
                    for enclave_idx, enclave_row in enclave_features.iterrows():
                        enclave_geom = enclave_row.geometry
                        
                        # Check if enclave is completely within boundary
                        if boundary_geom.contains(enclave_geom):
                            contained_enclaves.append(enclave_geom)
                    
                    # Create holes if there are contained enclaves
                    if contained_enclaves:
                        try:
                            # Create union of all contained enclaves
                            if len(contained_enclaves) == 1:
                                enclave_union = contained_enclaves[0]
                            else:
                                enclave_union = unary_union(contained_enclaves)
                            
                            # Create polygon with holes
                            if hasattr(enclave_union, 'geoms'):
                                # Multiple enclaves
                                holes = []
                                for geom in enclave_union.geoms:
                                    if hasattr(geom, 'exterior'):
                                        holes.append(list(geom.exterior.coords))
                            else:
                                # Single enclave
                                if hasattr(enclave_union, 'exterior'):
                                    holes = [list(enclave_union.exterior.coords)]
                                else:
                                    holes = []
                            
                            if holes:
                                # Create new polygon with holes
                                new_polygon = Polygon(boundary_geom.exterior.coords, holes)
                                gdf_result.at[boundary_idx, 'geometry'] = new_polygon
                                holes_created += 1
                                
                                self.log(f"  ✓ Created hole in boundary feature {boundary_row.get('ID_Feature', boundary_idx)}")
                        
                        except Exception as hole_error:
                            self.log(f"  Warning: Could not create hole for {blok}-{subdivisi}: {hole_error}")
            
            self.log(f"✓ Hole creation completed. {holes_created} holes created.")
            return gdf_result
            
        except Exception as e:
            self.log(f"Error during hole creation: {str(e)}")
            return gdf_boundary

    def create_advanced_polygon_holes(self):
        """Create advanced polygon holes (standalone function)"""
        if self.gdf_boundary is None:
            messagebox.showerror("Error", "Please load boundary data first!")
            return

        try:
            self.hole_results_text.delete(1.0, tk.END)
            self.hole_results_text.insert(tk.END, "Starting advanced polygon hole creation...\n\n")

            # Use current boundary data or updated data if available
            source_gdf = self.updated_gdf_boundary if self.updated_gdf_boundary is not None else self.gdf_boundary

            # Perform advanced hole creation
            gdf_with_holes = self.perform_advanced_hole_creation(source_gdf.copy())

            # Update the stored boundary
            self.updated_gdf_boundary = gdf_with_holes

            # Show results
            self.hole_results_text.insert(tk.END, "Advanced hole creation completed!\n")
            self.hole_results_text.insert(tk.END, "Results have been applied to the boundary shapefile.\n")
            self.hole_results_text.insert(tk.END, "Use 'Save Updated Shapefile' to save the results.\n\n")

            # Show summary
            boundary_count = len(gdf_with_holes[gdf_with_holes['HCV'] == 0])
            enclave_count = len(gdf_with_holes[gdf_with_holes['HCV'] == 1])

            summary = f"Summary:\n"
            summary += f"• Total features processed: {len(gdf_with_holes)}\n"
            summary += f"• Boundary polygons: {boundary_count}\n"
            summary += f"• Enclave polygons: {enclave_count}\n"
            summary += f"• Boundary-in-Boundary holes: {'Enabled' if self.boundary_holes_var.get() else 'Disabled'}\n"
            summary += f"• Enclave holes: {'Enabled' if self.enclave_holes_var.get() else 'Disabled'}\n"
            summary += f"• Multi-hole support: {'Enabled' if self.multi_holes_var.get() else 'Disabled'}\n"

            self.hole_results_text.insert(tk.END, summary)

        except Exception as e:
            error_msg = f"Error creating advanced holes: {str(e)}"
            self.hole_results_text.insert(tk.END, f"ERROR: {error_msg}\n")
            messagebox.showerror("Error", error_msg)

    def preview_advanced_holes(self):
        """Preview advanced hole creation results"""
        if self.gdf_boundary is None:
            messagebox.showerror("Error", "Please load boundary data first!")
            return

        try:
            self.hole_results_text.delete(1.0, tk.END)
            self.hole_results_text.insert(tk.END, "=== ADVANCED HOLE CREATION PREVIEW ===\n\n")

            source_gdf = self.updated_gdf_boundary if self.updated_gdf_boundary is not None else self.gdf_boundary

            # Preview boundary-in-boundary relationships
            if self.boundary_holes_var.get():
                self.hole_results_text.insert(tk.END, "1. BOUNDARY-IN-BOUNDARY ANALYSIS:\n")
                boundary_preview = self.preview_boundary_in_boundary(source_gdf)
                self.hole_results_text.insert(tk.END, boundary_preview + "\n")

            # Preview enclave relationships
            if self.enclave_holes_var.get():
                self.hole_results_text.insert(tk.END, "2. ENCLAVE HOLE ANALYSIS:\n")
                enclave_preview = self.preview_enclave_relationships(source_gdf)
                self.hole_results_text.insert(tk.END, enclave_preview + "\n")

            # Show configuration
            config_text = "3. CURRENT CONFIGURATION:\n"
            config_text += f"• Boundary-in-Boundary holes: {'✓ Enabled' if self.boundary_holes_var.get() else '✗ Disabled'}\n"
            config_text += f"• Enclave holes: {'✓ Enabled' if self.enclave_holes_var.get() else '✗ Disabled'}\n"
            config_text += f"• Multi-hole support: {'✓ Enabled' if self.multi_holes_var.get() else '✗ Disabled'}\n"

            self.hole_results_text.insert(tk.END, config_text)

        except Exception as e:
            error_msg = f"Error previewing advanced holes: {str(e)}"
            self.hole_results_text.insert(tk.END, f"ERROR: {error_msg}\n")

    def preview_boundary_in_boundary(self, gdf):
        """Preview boundary-in-boundary relationships"""
        try:
            # Identify boundary polygons
            boundary_mask = (gdf['HCV'] == 0)
            if 'ID_feature' in gdf.columns:
                id_boundary_mask = gdf['ID_feature'].astype(str).str.lower().str.startswith('boundary-')
                boundary_mask = boundary_mask | id_boundary_mask

            boundary_polygons = gdf[boundary_mask].copy()

            if len(boundary_polygons) < 2:
                return "   No boundary-in-boundary relationships possible (need at least 2 boundary polygons)\n"

            # Sort by area
            boundary_polygons['temp_area'] = boundary_polygons.geometry.area
            boundary_polygons = boundary_polygons.sort_values('temp_area', ascending=False)

            relationships = []

            for i, (outer_idx, outer_row) in enumerate(boundary_polygons.iterrows()):
                outer_geom = outer_row.geometry
                contained_count = 0

                for j, (inner_idx, inner_row) in enumerate(boundary_polygons.iterrows()):
                    if i >= j:  # Skip self and larger polygons
                        continue

                    inner_geom = inner_row.geometry

                    try:
                        if self.check_containment_with_tolerance(outer_geom, inner_geom):
                            contained_count += 1
                    except:
                        continue

                if contained_count > 0:
                    outer_id = outer_row.get('BLOK', f'Feature_{outer_idx}')
                    relationships.append(f"   • {outer_id}: contains {contained_count} smaller boundary polygon(s)")

            if relationships:
                result = f"   Found {len(relationships)} boundary polygons that contain other boundaries:\n"
                result += "\n".join(relationships) + "\n"
            else:
                result = "   No boundary-in-boundary containment relationships found\n"

            return result

        except Exception as e:
            return f"   Error analyzing boundary relationships: {str(e)}\n"

    def preview_enclave_relationships(self, gdf):
        """Preview enclave relationships"""
        try:
            boundary_polygons = gdf[gdf['HCV'] == 0]
            enclave_polygons = gdf[gdf['HCV'] == 1]

            if len(boundary_polygons) == 0:
                return "   No boundary polygons found\n"

            if len(enclave_polygons) == 0:
                return "   No enclave polygons found\n"

            relationships = []
            total_potential_holes = 0

            # Group by BLOK and SUBDIVISI
            for (blok, subdivisi), group in gdf.groupby(['BLOK', 'SUBDIVISI']):
                group_boundaries = group[group['HCV'] == 0]
                group_enclaves = group[group['HCV'] == 1]

                if len(group_boundaries) == 0 or len(group_enclaves) == 0:
                    continue

                group_holes = 0
                for _, boundary_row in group_boundaries.iterrows():
                    boundary_geom = boundary_row.geometry

                    for _, enclave_row in group_enclaves.iterrows():
                        enclave_geom = enclave_row.geometry

                        try:
                            if self.check_containment_with_tolerance(boundary_geom, enclave_geom):
                                group_holes += 1
                        except:
                            continue

                if group_holes > 0:
                    relationships.append(f"   • {blok}-{subdivisi}: {group_holes} potential enclave hole(s)")
                    total_potential_holes += group_holes

            if relationships:
                result = f"   Found {total_potential_holes} potential enclave holes in {len(relationships)} block(s):\n"
                result += "\n".join(relationships) + "\n"
            else:
                result = "   No enclave containment relationships found\n"

            return result

        except Exception as e:
            return f"   Error analyzing enclave relationships: {str(e)}\n"

    def run_comprehensive_analysis(self):
        """Run comprehensive boundary analysis with all fixes"""
        if self.gdf_boundary is None or self.gdf_points is None:
            messagebox.showerror("Error", "Please load both boundary and detection points data first!")
            return

        try:
            self.hole_results_text.delete(1.0, tk.END)
            self.hole_results_text.insert(tk.END, "🚀 STARTING COMPREHENSIVE ANALYSIS...\n\n")

            # Import the comprehensive analyzer
            from comprehensive_boundary_analysis import ComprehensiveBoundaryAnalyzer

            # Create analyzer instance
            analyzer = ComprehensiveBoundaryAnalyzer()

            # Set data directly
            analyzer.gdf_boundary = self.gdf_boundary.copy()
            analyzer.gdf_points = self.gdf_points.copy()

            # Standardize columns
            analyzer.standardize_columns()

            # Ensure same CRS
            if analyzer.gdf_boundary.crs != analyzer.gdf_points.crs:
                analyzer.gdf_points = analyzer.gdf_points.to_crs(analyzer.gdf_boundary.crs)

            # Convert to projected CRS
            if not analyzer.gdf_boundary.crs.is_projected:
                target_crs = 'EPSG:32748'
                analyzer.gdf_boundary = analyzer.gdf_boundary.to_crs(target_crs)
                analyzer.gdf_points = analyzer.gdf_points.to_crs(target_crs)
                self.hole_results_text.insert(tk.END, f"✅ Converted to projected CRS: {target_crs}\n")

            # Run analysis steps
            self.hole_results_text.insert(tk.END, "🔍 Analyzing nested boundaries...\n")
            nested_relationships = analyzer.analyze_nested_boundaries()

            self.hole_results_text.insert(tk.END, "🎯 Assigning points to closest boundaries...\n")
            point_assignments = analyzer.assign_points_to_closest_boundary()

            self.hole_results_text.insert(tk.END, "✂️ Splitting overlapping enclaves...\n")
            split_enclaves = analyzer.split_overlapping_enclaves()

            self.hole_results_text.insert(tk.END, "🕳️ Creating accurate holes...\n")
            gdf_with_holes = analyzer.create_accurate_holes()

            self.hole_results_text.insert(tk.END, "📊 Calculating comprehensive attributes...\n")
            gdf_final = analyzer.calculate_comprehensive_attributes(gdf_with_holes, point_assignments, split_enclaves)

            self.hole_results_text.insert(tk.END, "🔧 Fixing geometries for export...\n")
            gdf_export_ready = analyzer.fix_geometry_for_export(gdf_final)

            # Update the stored boundary data
            self.updated_gdf_boundary = gdf_export_ready

            # Show results
            boundary_count = len(gdf_export_ready[gdf_export_ready['HCV'] == 0])
            total_trees = gdf_export_ready['jumlah_pohon_updated'].sum()
            total_area = gdf_export_ready['luas_total'].sum()
            total_net_area = gdf_export_ready['luas_netto'].sum()

            summary = f"""
✅ COMPREHENSIVE ANALYSIS COMPLETED!

📊 RESULTS SUMMARY:
• Boundary polygons processed: {boundary_count}
• Total trees counted: {total_trees:,}
• Total area: {total_area:.2f} hectares
• Net plantable area: {total_net_area:.2f} hectares
• Nested relationships found: {len(nested_relationships)}
• Points assigned to boundaries: {len(point_assignments)}

🔧 FIXES APPLIED:
• Geometry export issues resolved
• Points assigned to closest boundaries
• Overlapping enclaves split properly
• Accurate hole creation implemented
• All attributes calculated correctly

💾 Ready for export! Use 'Save Updated Shapefile' to save results.
"""

            self.hole_results_text.insert(tk.END, summary)

            # Update other tabs with results
            self.update_results_from_comprehensive_analysis(gdf_export_ready, point_assignments)

        except Exception as e:
            error_msg = f"❌ Comprehensive analysis failed: {str(e)}"
            self.hole_results_text.insert(tk.END, f"\n{error_msg}\n")
            messagebox.showerror("Error", error_msg)
            import traceback
            self.hole_results_text.insert(tk.END, f"\nDetailed error:\n{traceback.format_exc()}")

    def update_results_from_comprehensive_analysis(self, gdf_result, point_assignments):
        """Update GUI results from comprehensive analysis"""
        try:
            # Create summary results
            summary_results = []

            for (blok, subdivisi), group in gdf_result.groupby(['BLOK', 'SUBDIVISI']):
                boundary_features = group[group['HCV'] == 0]
                enclave_features = group[group['HCV'] == 1]

                if len(boundary_features) > 0:
                    # Get totals for this block/subdivision
                    total_trees = boundary_features['jumlah_pohon_updated'].sum()
                    boundary_area = boundary_features['luas_total'].sum()
                    enclave_area = boundary_features['total_inclave'].sum()
                    net_area = boundary_features['luas_netto'].sum()

                    tree_density = total_trees / net_area if net_area > 0 else 0

                    summary_results.append({
                        'BLOK': blok,
                        'SUBDIVISI': subdivisi,
                        'TOTAL_POHON': int(total_trees),
                        'LUAS_BOUNDARY_HA': boundary_area,
                        'LUAS_ENCLAVE_HA': enclave_area,
                        'LUAS_NETTO_HA': net_area,
                        'KEPADATAN_POHON_PER_HA': tree_density,
                        'FITUR_BOUNDARY': len(boundary_features),
                        'FITUR_ENCLAVE': len(enclave_features)
                    })

            # Store results
            self.results_df = pd.DataFrame(summary_results)
            self.results_df = self.results_df.sort_values(['BLOK', 'SUBDIVISI'])

            # Update GUI tables
            self.update_summary_table()
            self.update_statistics()

        except Exception as e:
            self.log(f"Error updating results from comprehensive analysis: {e}")

    def create_polygon_holes(self):
        """Create polygon holes (standalone function)"""
        if self.updated_gdf_boundary is None:
            messagebox.showerror("Error", "Please run analysis first!")
            return
        
        try:
            self.hole_results_text.delete(1.0, tk.END)
            self.hole_results_text.insert(tk.END, "Starting polygon hole creation...\n\n")
            
            # Perform hole creation
            gdf_with_holes = self.perform_hole_creation(self.updated_gdf_boundary.copy())
            
            # Update the stored boundary
            self.updated_gdf_boundary = gdf_with_holes
            
            # Show results
            self.hole_results_text.insert(tk.END, "Hole creation completed!\n")
            self.hole_results_text.insert(tk.END, "Results have been applied to the boundary shapefile.\n")
            self.hole_results_text.insert(tk.END, "Use 'Save Updated Shapefile' to save the results.\n")
            
        except Exception as e:
            error_msg = f"Error creating holes: {str(e)}"
            self.hole_results_text.insert(tk.END, f"ERROR: {error_msg}\n")
            messagebox.showerror("Error", error_msg)

    def preview_holes(self):
        """Preview hole creation results"""
        if self.updated_gdf_boundary is None:
            messagebox.showerror("Error", "Please run analysis first!")
            return
        
        try:
            self.hole_results_text.delete(1.0, tk.END)
            self.hole_results_text.insert(tk.END, "=== HOLE CREATION PREVIEW ===\n\n")
            
            gdf = self.updated_gdf_boundary
            preview_results = []
            
            # Analyze potential holes
            for (blok, subdivisi), group in gdf.groupby(['BLOK', 'SUBDIVISI']):
                boundary_features = group[group['HCV'] == 0]
                enclave_features = group[group['HCV'] == 1]
                
                if len(boundary_features) > 0 and len(enclave_features) > 0:
                    boundary_count = len(boundary_features)
                    enclave_count = len(enclave_features)
                    
                    # Check spatial relationships
                    potential_holes = 0
                    for _, boundary_row in boundary_features.iterrows():
                        for _, enclave_row in enclave_features.iterrows():
                            if boundary_row.geometry.contains(enclave_row.geometry):
                                potential_holes += 1
                    
                    if potential_holes > 0:
                        preview_results.append({
                            'blok': blok,
                            'subdivisi': subdivisi,
                            'boundary_features': boundary_count,
                            'enclave_features': enclave_count,
                            'potential_holes': potential_holes
                        })
            
            # Display preview
            if preview_results:
                self.hole_results_text.insert(tk.END, "Blocks with potential holes:\n\n")
                for result in preview_results:
                    text = f"• {result['blok']} - {result['subdivisi']}:\n"
                    text += f"  - Boundary features: {result['boundary_features']}\n"
                    text += f"  - Enclave features: {result['enclave_features']}\n"
                    text += f"  - Potential holes: {result['potential_holes']}\n\n"
                    self.hole_results_text.insert(tk.END, text)
                
                total_potential = sum(r['potential_holes'] for r in preview_results)
                self.hole_results_text.insert(tk.END, f"Total potential holes: {total_potential}\n")
            else:
                self.hole_results_text.insert(tk.END, "No potential holes found.\n")
                self.hole_results_text.insert(tk.END, "This means no enclave polygons are completely contained within boundary polygons.\n")
            
        except Exception as e:
            error_msg = f"Error previewing holes: {str(e)}"
            self.hole_results_text.insert(tk.END, f"ERROR: {error_msg}\n")

    def save_updated_shapefile(self):
        """Save updated shapefile with ALL operations included by default"""
        # Check if we have data to work with
        if self.gdf_boundary is None:
            messagebox.showerror("Error", "Please load boundary data first!")
            return

        if self.gdf_points is None:
            messagebox.showerror("Error", "Please load detection points data first!")
            return
        
        # Ask user for save location
        filename = filedialog.asksaveasfilename(
            defaultextension='.shp',
            filetypes=[('Shapefile', '*.shp'), ('All files', '*.*')],
            title='Save Updated Boundary Shapefile'
        )
        
        if filename:
            try:
                self.log("🚀 STARTING COMPLETE ANALYSIS WITH ALL OPERATIONS...")
                self.log("=" * 60)

                # Run comprehensive analysis with all operations enabled by default
                gdf_to_save = self.run_complete_analysis_for_save()

                if gdf_to_save is None:
                    messagebox.showerror("Error", "Failed to complete analysis!")
                    return

                # Fix all geometry issues for export
                self.log("🔧 Fixing geometries for shapefile export...")
                gdf_to_save = self.fix_all_geometry_types(gdf_to_save)
                
                # Save the processed shapefile
                gdf_to_save.to_file(filename, driver='ESRI Shapefile')
                self.log(f"✅ Complete analysis shapefile saved successfully!")
                
                # Log success
                self.log(f"✓ Updated shapefile saved to: {filename}")
                
                # Show confirmation
                success_msg = f"Updated shapefile saved successfully!\n\n"
                success_msg += f"Location: {filename}\n\n"
                success_msg += f"The shapefile includes:\n"
                success_msg += f"• Updated tree counts\n"
                success_msg += f"• Updated area calculations\n"
                success_msg += f"• New attribute fields\n"
                success_msg += f"• Polygon holes (if created)\n"
                
                # Remove reference to undefined variable
                
                messagebox.showinfo("Success", success_msg)
                
                # Show summary of what was saved
                boundary_count = len(gdf_to_save[gdf_to_save['HCV'] == 0])
                enclave_count = len(gdf_to_save[gdf_to_save['HCV'] == 1])
                
                # Calculate summary statistics
                total_trees = gdf_to_save['jumlah_pohon_updated'].sum() if 'jumlah_pohon_updated' in gdf_to_save.columns else 0
                total_area = gdf_to_save['luas_total'].sum() if 'luas_total' in gdf_to_save.columns else 0
                net_area = gdf_to_save['luas_netto'].sum() if 'luas_netto' in gdf_to_save.columns else 0

                summary = f"""
🎉 COMPLETE ANALYSIS RESULTS SAVED:
{'='*50}
📊 FEATURES PROCESSED:
• Total features: {len(gdf_to_save)}
• Boundary features (HCV=0): {boundary_count}
• Enclave features (HCV=1): {enclave_count}

📈 CALCULATED RESULTS:
• Total trees: {total_trees:,}
• Total area: {total_area:.2f} hectares
• Net plantable area: {net_area:.2f} hectares

✅ OPERATIONS COMPLETED:
• Area calculations (luas_total, luas_netto)
• Tree counting (jumlah_pohon_updated)
• Boundary-in-boundary holes
• Enclave holes
• Geometry fixes for export

💾 SAVED TO: {filename}
{'='*50}
"""
                self.log(summary)
                
            except Exception as e:
                error_msg = f"Error saving shapefile: {str(e)}"
                self.log(error_msg)
                messagebox.showerror("Error", error_msg)

    def run_complete_analysis_for_save(self):
        """Run complete analysis with all operations enabled by default"""
        try:
            self.log("📊 Step 1: Preparing data...")

            # Start with original boundary data
            gdf_boundary = self.gdf_boundary.copy()
            gdf_points = self.gdf_points.copy()

            # Standardize columns
            column_mappings = {
                'SUB_DIVISI': 'SUBDIVISI',
                'HCV_Catego': 'HCV',
                'Jumlah_Poh': 'JUMLAH_POH'
            }

            for old, new in column_mappings.items():
                if old in gdf_boundary.columns and new not in gdf_boundary.columns:
                    gdf_boundary = gdf_boundary.rename(columns={old: new})

            # Clean HCV column
            if 'HCV' in gdf_boundary.columns:
                gdf_boundary['HCV'] = pd.to_numeric(gdf_boundary['HCV'], errors='coerce').fillna(0).astype(int)

            # Ensure same CRS and convert to projected
            if gdf_boundary.crs != gdf_points.crs:
                gdf_points = gdf_points.to_crs(gdf_boundary.crs)

            if not gdf_boundary.crs.is_projected:
                target_crs = 'EPSG:32748'
                gdf_boundary = gdf_boundary.to_crs(target_crs)
                gdf_points = gdf_points.to_crs(target_crs)
                self.log(f"  ✅ Converted to projected CRS: {target_crs}")

            self.log("🎯 Step 2: Assigning points to closest boundaries...")

            # Point assignment to closest boundaries
            boundaries = gdf_boundary[gdf_boundary['HCV'] == 0].copy()
            point_assignments = []

            for point_idx, point_row in gdf_points.iterrows():
                point_geom = point_row.geometry
                containing_boundaries = []

                for boundary_idx, boundary_row in boundaries.iterrows():
                    try:
                        if boundary_row.geometry.contains(point_geom):
                            containing_boundaries.append({
                                'boundary_idx': boundary_idx,
                                'area': boundary_row.geometry.area
                            })
                    except:
                        continue

                if containing_boundaries:
                    smallest_boundary = min(containing_boundaries, key=lambda x: x['area'])
                    point_assignments.append({
                        'point_idx': point_idx,
                        'boundary_idx': smallest_boundary['boundary_idx']
                    })

            self.log(f"  ✅ Assigned {len(point_assignments)} points to closest boundaries")

            self.log("✂️ Step 3: Splitting overlapping enclaves...")

            # Split overlapping enclaves
            enclaves = gdf_boundary[gdf_boundary['HCV'] == 1].copy()
            new_enclave_parts = []

            for enclave_idx, enclave_row in enclaves.iterrows():
                enclave_geom = enclave_row.geometry
                intersecting_boundaries = []

                for boundary_idx, boundary_row in boundaries.iterrows():
                    try:
                        if boundary_row.geometry.intersects(enclave_geom):
                            intersection = boundary_row.geometry.intersection(enclave_geom)
                            if hasattr(intersection, 'area') and intersection.area > 0:
                                intersecting_boundaries.append({
                                    'boundary_idx': boundary_idx,
                                    'blok': boundary_row['BLOK'],
                                    'intersection': intersection
                                })
                    except:
                        continue

                if len(intersecting_boundaries) > 1:
                    for boundary_info in intersecting_boundaries:
                        new_part = enclave_row.copy()
                        new_part.geometry = boundary_info['intersection']
                        new_enclave_parts.append(new_part)
                else:
                    new_enclave_parts.append(enclave_row.copy())

            split_enclaves = gpd.GeoDataFrame(new_enclave_parts, crs=gdf_boundary.crs)
            self.log(f"  ✅ Processed enclave splitting")

            self.log("🕳️ Step 4: Creating holes (boundary-in-boundary and enclave)...")

            # Create holes
            gdf_result = gdf_boundary.copy()
            holes_created = 0

            # Boundary-in-boundary holes
            boundaries_sorted = boundaries.copy()
            boundaries_sorted['area'] = boundaries_sorted.geometry.area
            boundaries_sorted = boundaries_sorted.sort_values('area', ascending=False)

            for i, (outer_idx, outer_row) in enumerate(boundaries_sorted.iterrows()):
                outer_geom = outer_row.geometry
                contained_boundaries = []

                for j, (inner_idx, inner_row) in enumerate(boundaries_sorted.iterrows()):
                    if i >= j:
                        continue

                    inner_geom = inner_row.geometry
                    try:
                        if outer_geom.contains(inner_geom):
                            contained_boundaries.append(inner_geom)
                    except:
                        continue

                if contained_boundaries:
                    try:
                        holes = []
                        for contained_geom in contained_boundaries:
                            if contained_geom.geom_type == 'Polygon':
                                holes.append(list(contained_geom.exterior.coords))

                        if holes:
                            from shapely.geometry import Polygon
                            new_polygon = Polygon(outer_geom.exterior.coords, holes)
                            if new_polygon.is_valid:
                                gdf_result.at[outer_idx, 'geometry'] = new_polygon
                                holes_created += len(holes)
                    except:
                        continue

            # Enclave holes
            for (blok, subdivisi), group in gdf_result.groupby(['BLOK', 'SUBDIVISI']):
                boundary_features = group[group['HCV'] == 0]

                if len(boundary_features) == 0:
                    continue

                block_enclaves = split_enclaves[
                    (split_enclaves['BLOK'] == blok) &
                    (split_enclaves['SUBDIVISI'] == subdivisi)
                ]

                for boundary_idx, boundary_row in boundary_features.iterrows():
                    boundary_geom = boundary_row.geometry
                    contained_enclaves = []

                    for _, enclave_row in block_enclaves.iterrows():
                        enclave_geom = enclave_row.geometry
                        try:
                            if boundary_geom.contains(enclave_geom):
                                contained_enclaves.append(enclave_geom)
                        except:
                            continue

                    if contained_enclaves:
                        try:
                            holes = []
                            for enclave_geom in contained_enclaves:
                                if enclave_geom.geom_type == 'Polygon':
                                    holes.append(list(enclave_geom.exterior.coords))

                            if holes:
                                from shapely.geometry import Polygon
                                if boundary_geom.geom_type == 'Polygon':
                                    new_polygon = Polygon(boundary_geom.exterior.coords, holes)
                                    if new_polygon.is_valid:
                                        gdf_result.at[boundary_idx, 'geometry'] = new_polygon
                                        holes_created += len(holes)
                        except:
                            continue

            self.log(f"  ✅ Created {holes_created} holes")

            self.log("📊 Step 5: Calculating comprehensive attributes...")

            # Add required columns
            required_columns = ['luas_total', 'luas_netto', 'jumlah_pohon_updated', 'total_inclave']
            for col in required_columns:
                if col not in gdf_result.columns:
                    gdf_result[col] = 0.0

            # Calculate attributes for each boundary
            for idx, row in gdf_result.iterrows():
                if row['HCV'] != 0:
                    continue

                blok = row['BLOK']
                subdivisi = row['SUBDIVISI']
                boundary_geom = row.geometry

                # Calculate total area
                luas_total = boundary_geom.area / 10000  # m² to hectares

                # Count trees assigned to this boundary
                boundary_trees = len([p for p in point_assignments if p['boundary_idx'] == idx])

                # Calculate enclave area
                boundary_enclaves = split_enclaves[
                    (split_enclaves['BLOK'] == blok) &
                    (split_enclaves['SUBDIVISI'] == subdivisi)
                ]

                total_inclave = 0
                for _, enclave_row in boundary_enclaves.iterrows():
                    try:
                        if boundary_geom.contains(enclave_row.geometry):
                            total_inclave += enclave_row.geometry.area / 10000
                    except:
                        continue

                luas_netto = luas_total - total_inclave

                # Update attributes
                gdf_result.at[idx, 'luas_total'] = luas_total
                gdf_result.at[idx, 'luas_netto'] = luas_netto
                gdf_result.at[idx, 'jumlah_pohon_updated'] = boundary_trees
                gdf_result.at[idx, 'total_inclave'] = total_inclave

            self.log("✅ Complete analysis finished successfully!")

            # Store results for other methods
            self.updated_gdf_boundary = gdf_result

            return gdf_result

        except Exception as e:
            self.log(f"❌ Error in complete analysis: {str(e)}")
            import traceback
            self.log(traceback.format_exc())
            return None

    def convert_multipolygon_to_polygon(self, gdf):
        """Convert MultiPolygon geometries to Polygon by keeping the largest polygon"""
        from shapely.geometry import Polygon, MultiPolygon
        
        gdf_converted = gdf.copy()
        conversion_count = 0
        
        for idx, row in gdf_converted.iterrows():
            geom = row.geometry
            
            if geom is None:
                continue
                
            # Convert MultiPolygon to Polygon (use largest polygon)
            if isinstance(geom, MultiPolygon):
                # Find the largest polygon in the MultiPolygon
                largest_poly = max(geom.geoms, key=lambda p: p.area)
                gdf_converted.at[idx, 'geometry'] = largest_poly
                conversion_count += 1
                
        self.log(f"  - Converted {conversion_count} MultiPolygon geometries to Polygon")
        return gdf_converted

def main():
    """Main function"""
    root = tk.Tk()
    app = ARECCorrectedTreeCountGUI(root)
    root.mainloop()

if __name__ == '__main__':
    main() 
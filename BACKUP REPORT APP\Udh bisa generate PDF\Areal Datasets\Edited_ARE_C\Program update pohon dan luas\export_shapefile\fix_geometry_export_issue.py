"""
DIRECT FIX for Geometry Export Issue
Analyzes and fixes GEOMETRYCOLLECTION and MultiPolygon issues immediately
"""

import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon, MultiPolygon, GeometryCollection
from shapely.ops import unary_union
import os

def analyze_and_fix_geometry_issues():
    """Analyze geometry issues and provide immediate fix"""
    print("🔧 DIRECT GEOMETRY ISSUE ANALYSIS & FIX")
    print("=" * 50)
    
    # Test with your actual file
    shapefile_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
    
    if not os.path.exists(shapefile_path):
        print(f"❌ File not found: {shapefile_path}")
        return None
    
    # Load and analyze
    print("📂 Loading shapefile...")
    gdf = gpd.read_file(shapefile_path)
    print(f"✅ Loaded {len(gdf)} features")
    
    # Analyze geometry types
    print("\n🔍 GEOMETRY TYPE ANALYSIS:")
    geom_types = gdf.geometry.geom_type.value_counts()
    print(geom_types)
    
    # Check for problematic geometries
    problematic_geoms = []
    for idx, geom in enumerate(gdf.geometry):
        geom_type = geom.geom_type
        if geom_type in ['GeometryCollection', 'MultiPolygon']:
            problematic_geoms.append((idx, geom_type, geom))
    
    print(f"\n⚠️  Found {len(problematic_geoms)} problematic geometries")
    
    if len(problematic_geoms) > 0:
        print("\nProblematic geometry details:")
        for idx, geom_type, geom in problematic_geoms[:5]:  # Show first 5
            print(f"  Index {idx}: {geom_type}")
            if hasattr(geom, 'geoms'):
                print(f"    Contains {len(geom.geoms)} sub-geometries")
    
    # Apply immediate fix
    print("\n🔧 APPLYING IMMEDIATE FIX...")
    gdf_fixed = fix_all_geometry_issues(gdf)
    
    # Test hole creation with fixed geometries
    print("\n🕳️  TESTING HOLE CREATION...")
    gdf_with_holes = test_hole_creation_direct(gdf_fixed)
    
    # Test export
    print("\n💾 TESTING EXPORT...")
    test_export_fixed_geometries(gdf_with_holes)
    
    return gdf_with_holes

def fix_all_geometry_issues(gdf):
    """Fix all geometry issues that prevent shapefile export"""
    print("Fixing geometry issues...")
    gdf_fixed = gdf.copy()
    
    fixed_count = 0
    for idx, geom in enumerate(gdf_fixed.geometry):
        original_type = geom.geom_type
        
        try:
            if geom.geom_type == 'GeometryCollection':
                # Extract only Polygon/MultiPolygon from GeometryCollection
                polygons = [g for g in geom.geoms if g.geom_type in ['Polygon', 'MultiPolygon']]
                if polygons:
                    if len(polygons) == 1:
                        fixed_geom = polygons[0]
                    else:
                        # Union multiple polygons
                        fixed_geom = unary_union(polygons)
                    
                    # Ensure it's a simple Polygon
                    if fixed_geom.geom_type == 'MultiPolygon':
                        # Take the largest polygon
                        largest_poly = max(fixed_geom.geoms, key=lambda x: x.area)
                        fixed_geom = largest_poly
                    
                    gdf_fixed.at[idx, 'geometry'] = fixed_geom
                    fixed_count += 1
                    print(f"  Fixed GeometryCollection at index {idx}")
            
            elif geom.geom_type == 'MultiPolygon':
                # Convert MultiPolygon to single Polygon (largest one)
                largest_poly = max(geom.geoms, key=lambda x: x.area)
                gdf_fixed.at[idx, 'geometry'] = largest_poly
                fixed_count += 1
                print(f"  Converted MultiPolygon to Polygon at index {idx}")
            
            # Validate the fixed geometry
            if not gdf_fixed.at[idx, 'geometry'].is_valid:
                # Try to fix invalid geometry
                fixed_geom = gdf_fixed.at[idx, 'geometry'].buffer(0)
                gdf_fixed.at[idx, 'geometry'] = fixed_geom
                print(f"  Fixed invalid geometry at index {idx}")
        
        except Exception as e:
            print(f"  ⚠️  Could not fix geometry at index {idx}: {e}")
            # Keep original geometry as fallback
            continue
    
    print(f"✅ Fixed {fixed_count} geometries")
    
    # Final validation
    final_types = gdf_fixed.geometry.geom_type.value_counts()
    print("Final geometry types:")
    print(final_types)
    
    return gdf_fixed

def test_hole_creation_direct(gdf):
    """Test hole creation with proper geometry handling"""
    print("Testing hole creation with fixed geometries...")
    
    try:
        # Standardize columns
        if 'HCV_Catego' in gdf.columns:
            gdf['HCV'] = pd.to_numeric(gdf['HCV_Catego'], errors='coerce').fillna(0).astype(int)
        
        gdf_result = gdf.copy()
        holes_created = 0
        
        # Simple enclave hole creation test
        for (blok, subdivisi), group in gdf.groupby(['BLOK', 'SUBDIVISI']):
            boundary_features = group[group['HCV'] == 0]
            enclave_features = group[group['HCV'] == 1]
            
            if len(boundary_features) == 0 or len(enclave_features) == 0:
                continue
            
            for boundary_idx, boundary_row in boundary_features.iterrows():
                boundary_geom = boundary_row.geometry
                
                # Ensure boundary is a simple Polygon
                if boundary_geom.geom_type != 'Polygon':
                    continue
                
                contained_enclaves = []
                for _, enclave_row in enclave_features.iterrows():
                    enclave_geom = enclave_row.geometry
                    
                    # Ensure enclave is a simple Polygon
                    if enclave_geom.geom_type != 'Polygon':
                        continue
                    
                    try:
                        if boundary_geom.contains(enclave_geom):
                            contained_enclaves.append(enclave_geom)
                    except Exception as e:
                        print(f"    Containment check failed: {e}")
                        continue
                
                # Create holes
                if contained_enclaves:
                    try:
                        holes = []
                        for enclave_geom in contained_enclaves:
                            if hasattr(enclave_geom, 'exterior'):
                                holes.append(list(enclave_geom.exterior.coords))
                        
                        if holes:
                            new_polygon = Polygon(boundary_geom.exterior.coords, holes)
                            
                            # Validate the new polygon
                            if new_polygon.is_valid:
                                gdf_result.at[boundary_idx, 'geometry'] = new_polygon
                                holes_created += 1
                                print(f"    ✅ Created hole in {blok}-{subdivisi}")
                            else:
                                print(f"    ⚠️  Invalid polygon created for {blok}-{subdivisi}")
                    
                    except Exception as hole_error:
                        print(f"    ❌ Hole creation failed for {blok}-{subdivisi}: {hole_error}")
        
        print(f"✅ Created {holes_created} holes successfully")
        return gdf_result
    
    except Exception as e:
        print(f"❌ Hole creation test failed: {e}")
        return gdf

def test_export_fixed_geometries(gdf):
    """Test export with comprehensive geometry validation"""
    print("Testing export with fixed geometries...")
    
    try:
        # Final geometry validation before export
        print("Final pre-export validation:")
        
        invalid_count = 0
        collection_count = 0
        multipolygon_count = 0
        
        for idx, geom in enumerate(gdf.geometry):
            if not geom.is_valid:
                invalid_count += 1
                # Try to fix
                gdf.at[idx, 'geometry'] = geom.buffer(0)
            
            if geom.geom_type == 'GeometryCollection':
                collection_count += 1
                print(f"  ⚠️  Still has GeometryCollection at index {idx}")
            
            if geom.geom_type == 'MultiPolygon':
                multipolygon_count += 1
                # Convert to single polygon
                largest_poly = max(geom.geoms, key=lambda x: x.area)
                gdf.at[idx, 'geometry'] = largest_poly
        
        print(f"  Invalid geometries fixed: {invalid_count}")
        print(f"  GeometryCollections remaining: {collection_count}")
        print(f"  MultiPolygons converted: {multipolygon_count}")
        
        # Test export
        output_path = "test_export_fixed.shp"
        gdf.to_file(output_path)
        print(f"✅ Export test successful: {output_path}")
        
        # Verify by reading back
        test_read = gpd.read_file(output_path)
        print(f"✅ Read-back test successful: {len(test_read)} features")
        
        return True
    
    except Exception as e:
        print(f"❌ Export test failed: {e}")
        return False

def create_fixed_gui_method():
    """Create the fixed method for the GUI"""
    method_code = '''
def save_updated_shapefile_fixed(self):
    """Fixed version of save_updated_shapefile with proper geometry handling"""
    if self.updated_gdf_boundary is None:
        messagebox.showerror("Error", "No updated data to save! Please run analysis first.")
        return
    
    filename = filedialog.asksaveasfilename(
        defaultextension='.shp',
        filetypes=[('Shapefile', '*.shp'), ('All files', '*.*')],
        title='Save Updated Boundary Shapefile'
    )
    
    if filename:
        try:
            # Create a copy for processing
            gdf_to_save = self.updated_gdf_boundary.copy()
            
            # Fix all geometry issues
            self.log("Fixing geometry issues for export...")
            fixed_count = 0
            
            for idx, geom in enumerate(gdf_to_save.geometry):
                try:
                    if geom.geom_type == 'GeometryCollection':
                        # Extract polygons from GeometryCollection
                        polygons = [g for g in geom.geoms if g.geom_type in ['Polygon', 'MultiPolygon']]
                        if polygons:
                            if len(polygons) == 1:
                                fixed_geom = polygons[0]
                            else:
                                from shapely.ops import unary_union
                                fixed_geom = unary_union(polygons)
                            
                            if fixed_geom.geom_type == 'MultiPolygon':
                                largest_poly = max(fixed_geom.geoms, key=lambda x: x.area)
                                fixed_geom = largest_poly
                            
                            gdf_to_save.at[idx, 'geometry'] = fixed_geom
                            fixed_count += 1
                    
                    elif geom.geom_type == 'MultiPolygon':
                        largest_poly = max(geom.geoms, key=lambda x: x.area)
                        gdf_to_save.at[idx, 'geometry'] = largest_poly
                        fixed_count += 1
                    
                    # Validate geometry
                    if not gdf_to_save.at[idx, 'geometry'].is_valid:
                        gdf_to_save.at[idx, 'geometry'] = gdf_to_save.at[idx, 'geometry'].buffer(0)
                
                except Exception as geom_error:
                    self.log(f"Could not fix geometry at index {idx}: {geom_error}")
                    continue
            
            self.log(f"Fixed {fixed_count} problematic geometries")
            
            # Save the fixed shapefile
            gdf_to_save.to_file(filename)
            
            self.log(f"✓ Updated shapefile saved to: {filename}")
            messagebox.showinfo("Success", f"Shapefile saved successfully!\\n\\nLocation: {filename}")
            
        except Exception as e:
            error_msg = f"Error saving shapefile: {str(e)}"
            self.log(error_msg)
            messagebox.showerror("Error", error_msg)
'''
    
    print("FIXED GUI METHOD:")
    print("=" * 50)
    print(method_code)
    print("=" * 50)
    print("Add this method to your GUI class to fix the export issue!")

def main():
    """Run immediate analysis and fix"""
    print("🚀 IMMEDIATE GEOMETRY ISSUE FIX")
    print("This will analyze and fix the export issue directly")
    print()
    
    # Run the analysis and fix
    result = analyze_and_fix_geometry_issues()
    
    if result is not None:
        print("\n✅ ANALYSIS COMPLETE - ISSUE IDENTIFIED AND FIXED")
        print("=" * 50)
        print("PROBLEM: GeometryCollection and MultiPolygon geometries")
        print("SOLUTION: Convert to simple Polygon geometries")
        print("RESULT: Export-ready shapefile")
        print()
        
        # Provide the fixed method
        create_fixed_gui_method()
    else:
        print("\n❌ ANALYSIS FAILED")

if __name__ == "__main__":
    main()

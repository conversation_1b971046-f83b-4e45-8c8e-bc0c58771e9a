"""
Test Comprehensive Solution
Quick test untuk memverifikasi solusi komprehensif
"""

import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon, MultiPolygon
from shapely.ops import unary_union
import os

def test_geometry_fix():
    """Test geometry fix untuk export"""
    print("🔧 TESTING GEOMETRY FIX")
    print("=" * 30)
    
    # Test file
    boundary_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
    
    if not os.path.exists(boundary_path):
        print("❌ File not found")
        return False
    
    try:
        # Load data
        gdf = gpd.read_file(boundary_path)
        print(f"✅ Loaded {len(gdf)} features")
        
        # Check geometry types
        geom_types = gdf.geometry.geom_type.value_counts()
        print(f"Original geometry types: {dict(geom_types)}")
        
        # Apply comprehensive geometry fix
        gdf_fixed = fix_all_geometry_issues(gdf)
        
        # Check fixed geometry types
        fixed_types = gdf_fixed.geometry.geom_type.value_counts()
        print(f"Fixed geometry types: {dict(fixed_types)}")
        
        # Test export
        output_path = "test_comprehensive_export.shp"
        gdf_fixed.to_file(output_path)
        print(f"✅ Export successful: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def fix_all_geometry_issues(gdf):
    """Comprehensive geometry fix"""
    print("Fixing all geometry issues...")
    
    gdf_fixed = gdf.copy()
    fixed_count = 0
    
    for idx, geom in enumerate(gdf_fixed.geometry):
        try:
            # Fix GeometryCollection
            if geom.geom_type == 'GeometryCollection':
                polygons = [g for g in geom.geoms if g.geom_type in ['Polygon', 'MultiPolygon']]
                if polygons:
                    if len(polygons) == 1:
                        fixed_geom = polygons[0]
                    else:
                        fixed_geom = unary_union(polygons)
                    
                    if fixed_geom.geom_type == 'MultiPolygon':
                        largest_poly = max(fixed_geom.geoms, key=lambda x: x.area)
                        fixed_geom = largest_poly
                    
                    gdf_fixed.at[idx, 'geometry'] = fixed_geom
                    fixed_count += 1
                    print(f"  Fixed GeometryCollection at index {idx}")
            
            # Fix MultiPolygon
            elif geom.geom_type == 'MultiPolygon':
                largest_poly = max(geom.geoms, key=lambda x: x.area)
                gdf_fixed.at[idx, 'geometry'] = largest_poly
                fixed_count += 1
                print(f"  Converted MultiPolygon at index {idx}")
            
            # Validate geometry
            if not gdf_fixed.at[idx, 'geometry'].is_valid:
                gdf_fixed.at[idx, 'geometry'] = gdf_fixed.at[idx, 'geometry'].buffer(0)
                print(f"  Fixed invalid geometry at index {idx}")
                
        except Exception as e:
            print(f"  Error fixing geometry {idx}: {e}")
            continue
    
    print(f"✅ Fixed {fixed_count} geometries")
    return gdf_fixed

def test_hole_creation():
    """Test hole creation logic"""
    print("\n🕳️  TESTING HOLE CREATION")
    print("=" * 30)
    
    try:
        # Load data
        boundary_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
        gdf = gpd.read_file(boundary_path)
        
        # Standardize columns
        if 'HCV_Catego' in gdf.columns:
            gdf['HCV'] = pd.to_numeric(gdf['HCV_Catego'], errors='coerce').fillna(0).astype(int)
        
        # Convert to projected CRS
        if not gdf.crs.is_projected:
            gdf = gdf.to_crs('EPSG:32748')
        
        # Test hole creation
        holes_created = 0
        
        for (blok, subdivisi), group in gdf.groupby(['BLOK', 'SUBDIVISI']):
            boundaries = group[group['HCV'] == 0]
            enclaves = group[group['HCV'] == 1]
            
            if len(boundaries) == 0 or len(enclaves) == 0:
                continue
            
            for boundary_idx, boundary_row in boundaries.iterrows():
                boundary_geom = boundary_row.geometry
                contained_enclaves = []
                
                for _, enclave_row in enclaves.iterrows():
                    enclave_geom = enclave_row.geometry
                    
                    try:
                        if boundary_geom.contains(enclave_geom):
                            contained_enclaves.append(enclave_geom)
                    except:
                        continue
                
                if contained_enclaves:
                    try:
                        holes = []
                        for enclave_geom in contained_enclaves:
                            if enclave_geom.geom_type == 'Polygon':
                                holes.append(list(enclave_geom.exterior.coords))
                        
                        if holes:
                            new_polygon = Polygon(boundary_geom.exterior.coords, holes)
                            if new_polygon.is_valid:
                                holes_created += 1
                                print(f"  ✅ Created hole in {blok}-{subdivisi}")
                                break  # Test one hole per block
                    except Exception as e:
                        print(f"  ❌ Hole creation failed for {blok}-{subdivisi}: {e}")
            
            if holes_created >= 3:  # Test max 3 holes
                break
        
        print(f"✅ Successfully created {holes_created} test holes")
        return holes_created > 0
        
    except Exception as e:
        print(f"❌ Hole creation test failed: {e}")
        return False

def test_point_assignment():
    """Test point assignment to closest boundaries"""
    print("\n🎯 TESTING POINT ASSIGNMENT")
    print("=" * 30)
    
    try:
        # Load data
        boundary_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
        points_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"
        
        if not os.path.exists(points_path):
            print("❌ Points file not found")
            return False
        
        gdf_boundary = gpd.read_file(boundary_path)
        gdf_points = gpd.read_file(points_path)
        
        print(f"Loaded {len(gdf_boundary)} boundaries and {len(gdf_points)} points")
        
        # Standardize and convert CRS
        if 'HCV_Catego' in gdf_boundary.columns:
            gdf_boundary['HCV'] = pd.to_numeric(gdf_boundary['HCV_Catego'], errors='coerce').fillna(0).astype(int)
        
        if gdf_boundary.crs != gdf_points.crs:
            gdf_points = gdf_points.to_crs(gdf_boundary.crs)
        
        if not gdf_boundary.crs.is_projected:
            target_crs = 'EPSG:32748'
            gdf_boundary = gdf_boundary.to_crs(target_crs)
            gdf_points = gdf_points.to_crs(target_crs)
        
        # Test point assignment (sample)
        boundaries = gdf_boundary[gdf_boundary['HCV'] == 0]
        sample_points = gdf_points.head(100)  # Test with 100 points
        
        assignments = 0
        for _, point_row in sample_points.iterrows():
            point_geom = point_row.geometry
            containing_boundaries = []
            
            for boundary_idx, boundary_row in boundaries.iterrows():
                try:
                    if boundary_row.geometry.contains(point_geom):
                        containing_boundaries.append({
                            'boundary_idx': boundary_idx,
                            'area': boundary_row.geometry.area
                        })
                except:
                    continue
            
            if containing_boundaries:
                # Assign to smallest boundary (closest)
                smallest = min(containing_boundaries, key=lambda x: x['area'])
                assignments += 1
        
        print(f"✅ Successfully assigned {assignments} out of {len(sample_points)} test points")
        return assignments > 0
        
    except Exception as e:
        print(f"❌ Point assignment test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 COMPREHENSIVE SOLUTION TEST")
    print("=" * 50)
    
    tests = [
        ("Geometry Fix", test_geometry_fix),
        ("Hole Creation", test_hole_creation),
        ("Point Assignment", test_point_assignment)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print(f"{'='*50}")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED!")
        print("The comprehensive solution is working correctly.")
        print("You can now use the enhanced GUI with confidence.")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed.")
        print("Please check the error messages above.")

if __name__ == "__main__":
    main()

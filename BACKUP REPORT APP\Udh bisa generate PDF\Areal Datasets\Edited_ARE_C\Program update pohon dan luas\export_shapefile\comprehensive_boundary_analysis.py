"""
COMPREHENSIVE BOUNDARY ANALYSIS & HOLE CREATION
Solusi lengkap untuk masalah geometry dan analisis spatial yang akurat
"""

import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon, MultiPolygon, Point
from shapely.ops import unary_union
import os
import numpy as np

class ComprehensiveBoundaryAnalyzer:
    def __init__(self):
        self.gdf_boundary = None
        self.gdf_points = None
        self.results = []
        
    def load_data(self, boundary_path, points_path):
        """Load boundary and detection points data"""
        print("🔄 LOADING DATA...")
        
        # Load boundary
        self.gdf_boundary = gpd.read_file(boundary_path)
        print(f"✅ Loaded {len(self.gdf_boundary)} boundary polygons")
        
        # Load detection points
        self.gdf_points = gpd.read_file(points_path)
        print(f"✅ Loaded {len(self.gdf_points)} detection points")
        
        # Standardize columns
        self.standardize_columns()
        
        # Ensure same CRS
        if self.gdf_boundary.crs != self.gdf_points.crs:
            self.gdf_points = self.gdf_points.to_crs(self.gdf_boundary.crs)
        
        # Convert to projected CRS for accurate calculations
        if not self.gdf_boundary.crs.is_projected:
            target_crs = 'EPSG:32748'  # UTM Zone 48S for Indonesia
            self.gdf_boundary = self.gdf_boundary.to_crs(target_crs)
            self.gdf_points = self.gdf_points.to_crs(target_crs)
            print(f"✅ Converted to projected CRS: {target_crs}")
    
    def standardize_columns(self):
        """Standardize column names"""
        mappings = {
            'SUB_DIVISI': 'SUBDIVISI',
            'HCV_Catego': 'HCV',
            'Jumlah_Poh': 'JUMLAH_POH'
        }
        
        for old, new in mappings.items():
            if old in self.gdf_boundary.columns and new not in self.gdf_boundary.columns:
                self.gdf_boundary = self.gdf_boundary.rename(columns={old: new})
        
        # Clean HCV column
        if 'HCV' in self.gdf_boundary.columns:
            self.gdf_boundary['HCV'] = pd.to_numeric(self.gdf_boundary['HCV'], errors='coerce').fillna(0).astype(int)
    
    def analyze_nested_boundaries(self):
        """Analyze which boundaries are nested within others"""
        print("\n🔍 ANALYZING NESTED BOUNDARIES...")
        
        boundaries = self.gdf_boundary[self.gdf_boundary['HCV'] == 0].copy()
        boundaries['area'] = boundaries.geometry.area
        boundaries = boundaries.sort_values('area', ascending=False)  # Largest first
        
        nested_relationships = []
        
        for i, (outer_idx, outer_row) in enumerate(boundaries.iterrows()):
            outer_geom = outer_row.geometry
            
            for j, (inner_idx, inner_row) in enumerate(boundaries.iterrows()):
                if i >= j:  # Skip self and larger polygons
                    continue
                
                inner_geom = inner_row.geometry
                
                try:
                    if outer_geom.contains(inner_geom):
                        nested_relationships.append({
                            'outer_idx': outer_idx,
                            'inner_idx': inner_idx,
                            'outer_blok': outer_row['BLOK'],
                            'inner_blok': inner_row['BLOK'],
                            'outer_area': outer_row['area'],
                            'inner_area': inner_row['area']
                        })
                        print(f"  📍 {inner_row['BLOK']} is nested within {outer_row['BLOK']}")
                except Exception as e:
                    continue
        
        print(f"✅ Found {len(nested_relationships)} nested boundary relationships")
        return nested_relationships
    
    def assign_points_to_closest_boundary(self):
        """Assign each detection point to its closest containing boundary"""
        print("\n🎯 ASSIGNING POINTS TO CLOSEST BOUNDARIES...")
        
        boundaries = self.gdf_boundary[self.gdf_boundary['HCV'] == 0].copy()
        point_assignments = []
        
        for point_idx, point_row in self.gdf_points.iterrows():
            point_geom = point_row.geometry
            containing_boundaries = []
            
            # Find all boundaries that contain this point
            for boundary_idx, boundary_row in boundaries.iterrows():
                try:
                    if boundary_row.geometry.contains(point_geom):
                        containing_boundaries.append({
                            'boundary_idx': boundary_idx,
                            'blok': boundary_row['BLOK'],
                            'area': boundary_row.geometry.area
                        })
                except:
                    continue
            
            # Assign to smallest containing boundary (closest/most specific)
            if containing_boundaries:
                smallest_boundary = min(containing_boundaries, key=lambda x: x['area'])
                point_assignments.append({
                    'point_idx': point_idx,
                    'boundary_idx': smallest_boundary['boundary_idx'],
                    'blok': smallest_boundary['blok']
                })
        
        print(f"✅ Assigned {len(point_assignments)} points to boundaries")
        return point_assignments
    
    def split_overlapping_enclaves(self):
        """Split enclaves that cross multiple boundaries"""
        print("\n✂️  SPLITTING OVERLAPPING ENCLAVES...")
        
        boundaries = self.gdf_boundary[self.gdf_boundary['HCV'] == 0].copy()
        enclaves = self.gdf_boundary[self.gdf_boundary['HCV'] == 1].copy()
        
        new_enclave_parts = []
        split_count = 0
        
        for enclave_idx, enclave_row in enclaves.iterrows():
            enclave_geom = enclave_row.geometry
            intersecting_boundaries = []
            
            # Find boundaries that intersect with this enclave
            for boundary_idx, boundary_row in boundaries.iterrows():
                try:
                    if boundary_row.geometry.intersects(enclave_geom):
                        intersection = boundary_row.geometry.intersection(enclave_geom)
                        if intersection.area > 0:  # Meaningful intersection
                            intersecting_boundaries.append({
                                'boundary_idx': boundary_idx,
                                'blok': boundary_row['BLOK'],
                                'intersection': intersection
                            })
                except:
                    continue
            
            # If enclave intersects multiple boundaries, split it
            if len(intersecting_boundaries) > 1:
                split_count += 1
                print(f"  ✂️  Splitting enclave across {len(intersecting_boundaries)} boundaries")
                
                for boundary_info in intersecting_boundaries:
                    # Create new enclave part for each boundary
                    new_part = enclave_row.copy()
                    new_part.geometry = boundary_info['intersection']
                    new_part['parent_boundary'] = boundary_info['blok']
                    new_enclave_parts.append(new_part)
            else:
                # Keep original enclave
                enclave_copy = enclave_row.copy()
                if intersecting_boundaries:
                    enclave_copy['parent_boundary'] = intersecting_boundaries[0]['blok']
                else:
                    enclave_copy['parent_boundary'] = 'NONE'
                new_enclave_parts.append(enclave_copy)
        
        print(f"✅ Split {split_count} overlapping enclaves")
        return gpd.GeoDataFrame(new_enclave_parts, crs=self.gdf_boundary.crs)
    
    def create_accurate_holes(self):
        """Create holes with proper geometry handling"""
        print("\n🕳️  CREATING ACCURATE HOLES...")
        
        gdf_result = self.gdf_boundary.copy()
        holes_created = 0
        
        # Get split enclaves
        split_enclaves = self.split_overlapping_enclaves()
        
        # Group by BLOK and SUBDIVISI
        for (blok, subdivisi), group in gdf_result.groupby(['BLOK', 'SUBDIVISI']):
            boundary_features = group[group['HCV'] == 0]
            
            if len(boundary_features) == 0:
                continue
            
            # Get enclaves for this block
            block_enclaves = split_enclaves[
                (split_enclaves['BLOK'] == blok) & 
                (split_enclaves['SUBDIVISI'] == subdivisi)
            ]
            
            for boundary_idx, boundary_row in boundary_features.iterrows():
                boundary_geom = boundary_row.geometry
                contained_enclaves = []
                
                # Find enclaves contained in this boundary
                for _, enclave_row in block_enclaves.iterrows():
                    enclave_geom = enclave_row.geometry
                    
                    try:
                        if boundary_geom.contains(enclave_geom):
                            contained_enclaves.append(enclave_geom)
                    except:
                        continue
                
                # Create holes
                if contained_enclaves:
                    try:
                        holes = []
                        for enclave_geom in contained_enclaves:
                            if enclave_geom.geom_type == 'Polygon':
                                holes.append(list(enclave_geom.exterior.coords))
                            elif enclave_geom.geom_type == 'MultiPolygon':
                                # Add all parts as separate holes
                                for poly in enclave_geom.geoms:
                                    holes.append(list(poly.exterior.coords))
                        
                        if holes:
                            # Create polygon with holes
                            if boundary_geom.geom_type == 'Polygon':
                                new_polygon = Polygon(boundary_geom.exterior.coords, holes)
                            else:
                                # Handle MultiPolygon - take largest part and add holes
                                largest_part = max(boundary_geom.geoms, key=lambda x: x.area)
                                new_polygon = Polygon(largest_part.exterior.coords, holes)
                            
                            # Validate geometry
                            if new_polygon.is_valid:
                                gdf_result.at[boundary_idx, 'geometry'] = new_polygon
                                holes_created += len(holes)
                                print(f"    ✅ Created {len(holes)} holes in {blok}-{subdivisi}")
                            else:
                                print(f"    ⚠️  Invalid geometry created for {blok}-{subdivisi}")
                    
                    except Exception as e:
                        print(f"    ❌ Hole creation failed for {blok}-{subdivisi}: {e}")
        
        print(f"✅ Total holes created: {holes_created}")
        return gdf_result
    
    def calculate_comprehensive_attributes(self, gdf_with_holes, point_assignments, split_enclaves):
        """Calculate all required attributes"""
        print("\n📊 CALCULATING COMPREHENSIVE ATTRIBUTES...")
        
        # Add new columns
        required_columns = ['luas_total', 'luas_netto', 'jumlah_pohon_updated', 'total_inclave']
        for col in required_columns:
            if col not in gdf_with_holes.columns:
                gdf_with_holes[col] = 0.0
        
        # Calculate for each boundary
        for idx, row in gdf_with_holes.iterrows():
            if row['HCV'] != 0:  # Only process boundaries
                continue
            
            blok = row['BLOK']
            subdivisi = row['SUBDIVISI']
            boundary_geom = row.geometry
            
            # Calculate total area (from geometry)
            luas_total = boundary_geom.area / 10000  # m² to hectares
            
            # Count trees assigned to this boundary
            boundary_trees = len([p for p in point_assignments if p['boundary_idx'] == idx])
            
            # Calculate enclave area within this boundary
            boundary_enclaves = split_enclaves[
                (split_enclaves['BLOK'] == blok) & 
                (split_enclaves['SUBDIVISI'] == subdivisi)
            ]
            
            total_inclave = 0
            for _, enclave_row in boundary_enclaves.iterrows():
                try:
                    if boundary_geom.contains(enclave_row.geometry):
                        total_inclave += enclave_row.geometry.area / 10000
                except:
                    continue
            
            # Calculate net area
            luas_netto = luas_total - total_inclave
            
            # Update attributes
            gdf_with_holes.at[idx, 'luas_total'] = luas_total
            gdf_with_holes.at[idx, 'luas_netto'] = luas_netto
            gdf_with_holes.at[idx, 'jumlah_pohon_updated'] = boundary_trees
            gdf_with_holes.at[idx, 'total_inclave'] = total_inclave
            
            print(f"  📍 {blok}-{subdivisi}: {luas_total:.2f}ha total, {total_inclave:.2f}ha inclave, {luas_netto:.2f}ha net, {boundary_trees} trees")
        
        return gdf_with_holes
    
    def fix_geometry_for_export(self, gdf):
        """Fix all geometry issues for shapefile export"""
        print("\n🔧 FIXING GEOMETRIES FOR EXPORT...")
        
        gdf_fixed = gdf.copy()
        fixed_count = 0
        
        for idx, geom in enumerate(gdf_fixed.geometry):
            try:
                original_type = geom.geom_type
                
                # Fix GeometryCollection
                if geom.geom_type == 'GeometryCollection':
                    polygons = [g for g in geom.geoms if g.geom_type in ['Polygon', 'MultiPolygon']]
                    if polygons:
                        if len(polygons) == 1:
                            fixed_geom = polygons[0]
                        else:
                            fixed_geom = unary_union(polygons)
                        
                        if fixed_geom.geom_type == 'MultiPolygon':
                            largest_poly = max(fixed_geom.geoms, key=lambda x: x.area)
                            fixed_geom = largest_poly
                        
                        gdf_fixed.at[idx, 'geometry'] = fixed_geom
                        fixed_count += 1
                        print(f"  🔧 Fixed GeometryCollection at index {idx}")
                
                # Fix MultiPolygon
                elif geom.geom_type == 'MultiPolygon':
                    largest_poly = max(geom.geoms, key=lambda x: x.area)
                    gdf_fixed.at[idx, 'geometry'] = largest_poly
                    fixed_count += 1
                    print(f"  🔧 Converted MultiPolygon at index {idx}")
                
                # Validate geometry
                if not gdf_fixed.at[idx, 'geometry'].is_valid:
                    gdf_fixed.at[idx, 'geometry'] = gdf_fixed.at[idx, 'geometry'].buffer(0)
                    print(f"  🔧 Fixed invalid geometry at index {idx}")
                    
            except Exception as e:
                print(f"  ❌ Could not fix geometry at index {idx}: {e}")
                continue
        
        print(f"✅ Fixed {fixed_count} geometries for export")
        return gdf_fixed
    
    def run_comprehensive_analysis(self, boundary_path, points_path, output_path):
        """Run complete analysis"""
        print("🚀 STARTING COMPREHENSIVE BOUNDARY ANALYSIS")
        print("=" * 60)
        
        # Load data
        self.load_data(boundary_path, points_path)
        
        # Analyze nested boundaries
        nested_relationships = self.analyze_nested_boundaries()
        
        # Assign points to closest boundaries
        point_assignments = self.assign_points_to_closest_boundary()
        
        # Split overlapping enclaves
        split_enclaves = self.split_overlapping_enclaves()
        
        # Create accurate holes
        gdf_with_holes = self.create_accurate_holes()
        
        # Calculate comprehensive attributes
        gdf_final = self.calculate_comprehensive_attributes(gdf_with_holes, point_assignments, split_enclaves)
        
        # Fix geometries for export
        gdf_export_ready = self.fix_geometry_for_export(gdf_final)
        
        # Export results
        print(f"\n💾 EXPORTING RESULTS TO: {output_path}")
        gdf_export_ready.to_file(output_path)
        print("✅ Export completed successfully!")
        
        # Summary
        boundary_count = len(gdf_export_ready[gdf_export_ready['HCV'] == 0])
        total_trees = gdf_export_ready['jumlah_pohon_updated'].sum()
        total_area = gdf_export_ready['luas_total'].sum()
        total_net_area = gdf_export_ready['luas_netto'].sum()
        
        print(f"\n📊 FINAL SUMMARY:")
        print(f"  • Boundary polygons processed: {boundary_count}")
        print(f"  • Total trees counted: {total_trees:,}")
        print(f"  • Total area: {total_area:.2f} hectares")
        print(f"  • Net plantable area: {total_net_area:.2f} hectares")
        print(f"  • Nested relationships: {len(nested_relationships)}")
        print(f"  • Points assigned: {len(point_assignments)}")
        
        return gdf_export_ready

def main():
    """Run comprehensive analysis"""
    # File paths
    boundary_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
    points_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"
    output_path = "ARE_C_COMPREHENSIVE_ANALYSIS_RESULT.shp"
    
    # Run analysis
    analyzer = ComprehensiveBoundaryAnalyzer()
    result = analyzer.run_comprehensive_analysis(boundary_path, points_path, output_path)
    
    print("\n🎉 COMPREHENSIVE ANALYSIS COMPLETED!")
    print("All issues have been resolved:")
    print("✅ Geometry export issues fixed")
    print("✅ Nested boundary analysis completed")
    print("✅ Points assigned to closest boundaries")
    print("✅ Overlapping enclaves split properly")
    print("✅ Accurate hole creation implemented")
    print("✅ Comprehensive attributes calculated")

if __name__ == "__main__":
    main()

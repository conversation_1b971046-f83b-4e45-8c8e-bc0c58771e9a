"""
Debug script untuk menganalisis masalah area calculation
Script ini akan menganalisis data area dan memberikan informasi detail
"""

import geopandas as gpd
import pandas as pd
import os

def analyze_area_data():
    """Analyze area data in detail"""
    print("DEBUGGING AREA CALCULATION ISSUES")
    print("=" * 50)
    
    boundary_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
    
    if not os.path.exists(boundary_path):
        print(f"❌ File not found: {boundary_path}")
        return
    
    try:
        # Load data
        gdf = gpd.read_file(boundary_path)
        print(f"✅ Loaded {len(gdf)} polygons")
        print(f"CRS: {gdf.crs}")
        
        # Standardize column names
        boundary_mappings = {
            'SUB_DIVISI': 'SUBDIVISI',
            'HCV_Catego': 'HCV',
            'Jumlah_Poh': 'JUMLAH_POH'
        }
        
        for old_name, new_name in boundary_mappings.items():
            if old_name in gdf.columns and new_name not in gdf.columns:
                gdf = gdf.rename(columns={old_name: new_name})
                print(f"Renamed {old_name} → {new_name}")
        
        # Clean data types
        numeric_columns = ['JUMLAH_POH', 'LUAS_AUTO']
        for col in numeric_columns:
            if col in gdf.columns:
                gdf[col] = pd.to_numeric(gdf[col], errors='coerce').fillna(0)
        
        gdf['HCV'] = gdf['HCV'].astype(str)
        
        print(f"\nColumns after standardization: {list(gdf.columns)}")
        
        # Analyze area columns
        print(f"\n=== AREA ANALYSIS ===")
        area_columns = ['LUAS_AUTO', 'luas_aut_1', 'total_incl', 'luas_netto', 'luas_asss']
        
        for col in area_columns:
            if col in gdf.columns:
                print(f"\nColumn: {col}")
                print(f"  Data type: {gdf[col].dtype}")
                print(f"  Non-zero count: {(gdf[col] != 0).sum()}")
                print(f"  Total sum: {gdf[col].sum()}")
                print(f"  Min: {gdf[col].min()}")
                print(f"  Max: {gdf[col].max()}")
                print(f"  Sample values: {gdf[col].head(5).tolist()}")
        
        # Calculate geometry areas
        print(f"\n=== GEOMETRY AREA ANALYSIS ===")
        gdf['geom_area_sqm'] = gdf.geometry.area
        gdf['geom_area_ha'] = gdf['geom_area_sqm'] / 10000
        
        print(f"Geometry area (sq meters):")
        print(f"  Total: {gdf['geom_area_sqm'].sum():,.0f}")
        print(f"  Min: {gdf['geom_area_sqm'].min():,.0f}")
        print(f"  Max: {gdf['geom_area_sqm'].max():,.0f}")
        
        print(f"Geometry area (hectares):")
        print(f"  Total: {gdf['geom_area_ha'].sum():,.2f}")
        print(f"  Min: {gdf['geom_area_ha'].min():,.2f}")
        print(f"  Max: {gdf['geom_area_ha'].max():,.2f}")
        
        # Compare LUAS_AUTO vs geometry area
        if 'LUAS_AUTO' in gdf.columns:
            print(f"\n=== LUAS_AUTO vs GEOMETRY COMPARISON ===")
            print(f"LUAS_AUTO total: {gdf['LUAS_AUTO'].sum():,.2f}")
            print(f"Geometry area (ha): {gdf['geom_area_ha'].sum():,.2f}")
            
            # Check if LUAS_AUTO is in square meters
            if gdf['LUAS_AUTO'].sum() > gdf['geom_area_ha'].sum() * 100:
                print("LUAS_AUTO appears to be in square meters")
                gdf['LUAS_AUTO_ha'] = gdf['LUAS_AUTO'] / 10000
                print(f"LUAS_AUTO converted to hectares: {gdf['LUAS_AUTO_ha'].sum():,.2f}")
            else:
                print("LUAS_AUTO appears to be in hectares")
                gdf['LUAS_AUTO_ha'] = gdf['LUAS_AUTO']
        
        # Analyze by Block/Sub Division
        print(f"\n=== ANALYSIS BY BLOCK/SUBDIVISION ===")
        grouped = gdf.groupby(['BLOK', 'SUBDIVISI'])
        
        print(f"Number of Block/Sub Division combinations: {len(grouped)}")
        
        sample_groups = list(grouped)[:5]  # First 5 groups
        for (blok, subdivisi), group in sample_groups:
            print(f"\nGroup: {blok} - {subdivisi}")
            print(f"  Polygons: {len(group)}")
            print(f"  HCV values: {group['HCV'].unique()}")
            
            if 'LUAS_AUTO_ha' in group.columns:
                total_area = group['LUAS_AUTO_ha'].sum()
                print(f"  LUAS_AUTO total (ha): {total_area:.2f}")
            
            geom_area = group['geom_area_ha'].sum()
            print(f"  Geometry area (ha): {geom_area:.2f}")
            
            if 'JUMLAH_POH' in group.columns:
                tree_count = group['JUMLAH_POH'].sum()
                print(f"  Tree count: {tree_count}")
        
        # HCV pattern analysis
        print(f"\n=== HCV PATTERN ANALYSIS ===")
        hcv_values = gdf['HCV'].unique()
        print(f"Unique HCV values: {hcv_values}")
        
        boundary_count = len(gdf[gdf['HCV'].str.contains('Boundary', na=False)])
        inclave_count = len(gdf[gdf['HCV'].str.contains('inclave', case=False, na=False)])
        
        print(f"Boundary pattern matches: {boundary_count}")
        print(f"Inclave pattern matches: {inclave_count}")
        
        if boundary_count == 0:
            print("⚠️  No 'Boundary' patterns found - this explains why areas show as 0")
            print("All polygons should be treated as boundaries")
        
        # Sample detailed analysis
        print(f"\n=== SAMPLE DETAILED ANALYSIS ===")
        sample_data = gdf.head(3)
        for idx, row in sample_data.iterrows():
            print(f"\nPolygon {idx}:")
            print(f"  BLOK: {row['BLOK']}")
            print(f"  SUBDIVISI: {row['SUBDIVISI']}")
            print(f"  HCV: {row['HCV']}")
            if 'LUAS_AUTO' in row:
                print(f"  LUAS_AUTO: {row['LUAS_AUTO']}")
            if 'JUMLAH_POH' in row:
                print(f"  JUMLAH_POH: {row['JUMLAH_POH']}")
            print(f"  Geometry area (ha): {row['geom_area_ha']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run area analysis"""
    result = analyze_area_data()
    
    if result:
        print(f"\n" + "=" * 50)
        print("ANALYSIS COMPLETED")
        print("=" * 50)
        print("Check the output above to understand:")
        print("1. Which area column to use (LUAS_AUTO, geometry, etc.)")
        print("2. Whether areas are in square meters or hectares")
        print("3. Why HCV pattern matching might be failing")
        print("4. How to fix the area calculation logic")
    else:
        print(f"\n❌ Analysis failed")

if __name__ == "__main__":
    main()

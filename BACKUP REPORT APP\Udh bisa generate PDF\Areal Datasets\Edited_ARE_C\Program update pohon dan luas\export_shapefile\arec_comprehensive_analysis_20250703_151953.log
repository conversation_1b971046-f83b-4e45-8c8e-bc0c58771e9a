2025-07-03 15:19:53,744 INFO: Loading initial data...
2025-07-03 15:19:53,746 INFO: Loading boundary polygons...
2025-07-03 15:19:53,849 INFO: \u2713 Loaded 129 boundary polygons
2025-07-03 15:19:53,851 INFO:   CRS: EPSG:32748
2025-07-03 15:19:53,857 INFO:   Columns: ['Id', 'SUB_DIVISI', 'BLOK', 'LUAS_AUTO', '<PERSON><PERSON><PERSON>_Poh', 'HCV_Catego', 'ID_Feature', 'luas_aut_1', 'SPH', 'contained_', 'total_incl', 'luas_netto', 'IS_AGLIMAS', 'luas_asss', 'geometry']
2025-07-03 15:19:53,858 INFO: Loading detection points...
2025-07-03 15:19:56,740 INFO: \u2713 Loaded 243021 detection points
2025-07-03 15:19:56,741 INFO:   CRS: EPSG:32748
2025-07-03 15:19:56,742 INFO: Standardizing column names...
2025-07-03 15:19:56,753 INFO:   Renamed 'SUB_DIVISI' to 'SUBDIVISI' in boundary data
2025-07-03 15:19:56,755 INFO:   Renamed 'HCV_Catego' to 'HCV' in boundary data
2025-07-03 15:19:56,758 INFO:   Renamed 'Jumlah_Poh' to 'JUMLAH_POH' in boundary data
2025-07-03 15:19:56,761 INFO: Initial data loading completed successfully
2025-07-03 15:19:59,715 INFO: Loading initial data...
2025-07-03 15:19:59,717 INFO: Loading boundary polygons...
2025-07-03 15:19:59,727 INFO: \u2713 Loaded 129 boundary polygons
2025-07-03 15:19:59,728 INFO:   CRS: EPSG:32748
2025-07-03 15:19:59,734 INFO:   Columns: ['Id', 'SUB_DIVISI', 'BLOK', 'LUAS_AUTO', 'Jumlah_Poh', 'HCV_Catego', 'ID_Feature', 'luas_aut_1', 'SPH', 'contained_', 'total_incl', 'luas_netto', 'IS_AGLIMAS', 'luas_asss', 'geometry']
2025-07-03 15:19:59,735 INFO: Loading detection points...
2025-07-03 15:20:02,654 INFO: \u2713 Loaded 243021 detection points
2025-07-03 15:20:02,655 INFO:   CRS: EPSG:32748
2025-07-03 15:20:02,657 INFO: Standardizing column names...
2025-07-03 15:20:02,660 INFO:   Renamed 'SUB_DIVISI' to 'SUBDIVISI' in boundary data
2025-07-03 15:20:02,662 INFO:   Renamed 'HCV_Catego' to 'HCV' in boundary data
2025-07-03 15:20:02,665 INFO:   Renamed 'Jumlah_Poh' to 'JUMLAH_POH' in boundary data
2025-07-03 15:20:02,668 INFO: Initial data loading completed successfully

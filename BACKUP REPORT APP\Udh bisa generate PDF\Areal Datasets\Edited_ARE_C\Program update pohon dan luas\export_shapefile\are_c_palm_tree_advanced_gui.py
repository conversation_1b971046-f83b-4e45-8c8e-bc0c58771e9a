import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import geopandas as gpd
import pandas as pd
import os
import logging
from shapely.geometry import Polygon, MultiPolygon
from shapely.ops import unary_union
import threading

# --- Konstanta Path Data Default ---
DEFAULT_BOUNDARY_PATH = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
DEFAULT_POINTS_PATH = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"

class ARECPalmTreeAdvancedGUI:
    def __init__(self, root):
        self.root = root
        self.root.title('ARE C Palm Tree Detection - Advanced Spatial GUI')
        self.root.geometry('1400x900')
        self.root.minsize(1200, 800)

        # Data paths
        self.boundary_path = DEFAULT_BOUNDARY_PATH
        self.points_path = DEFAULT_POINTS_PATH
        self.gdf_boundary = None
        self.gdf_points = None
        self.df_summary = None
        self.processed_gdf = None
        self.processing = False
        self.cancel_flag = False
        self.log_file = 'arec_palm_tree_gui.log'
        self.setup_logging()
        self.create_gui()
        self.load_initial_data()

    def setup_logging(self):
        logging.basicConfig(filename=self.log_file, level=logging.INFO, 
                            format='%(asctime)s %(levelname)s: %(message)s')

    def log(self, msg):
        self.log_text.configure(state='normal')
        self.log_text.insert('end', msg + '\n')
        self.log_text.see('end')
        self.log_text.configure(state='disabled')
        logging.info(msg)

    def create_gui(self):
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True)
        # --- Tab Data ---
        self.data_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_frame, text='Data')
        self.create_data_tab()
        # --- Tab Analisis ---
        self.analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analysis_frame, text='Analisis')
        self.create_analysis_tab()
        # --- Tab Hasil ---
        self.results_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.results_frame, text='Hasil')
        self.create_results_tab()
        # --- Tab Log ---
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text='Log')
        self.create_log_tab()

    def create_data_tab(self):
        f = self.data_frame
        pad = {'padx': 10, 'pady': 5}
        # Path boundary
        ttk.Label(f, text='Boundary Polygon Shapefile:').grid(row=0, column=0, sticky='w', **pad)
        self.boundary_entry = ttk.Entry(f, width=100)
        self.boundary_entry.grid(row=0, column=1, **pad)
        self.boundary_entry.insert(0, self.boundary_path)
        ttk.Button(f, text='Browse', command=self.browse_boundary).grid(row=0, column=2, **pad)
        # Path points
        ttk.Label(f, text='Detection Points Shapefile:').grid(row=1, column=0, sticky='w', **pad)
        self.points_entry = ttk.Entry(f, width=100)
        self.points_entry.grid(row=1, column=1, **pad)
        self.points_entry.insert(0, self.points_path)
        ttk.Button(f, text='Browse', command=self.browse_points).grid(row=1, column=2, **pad)
        # Reload button
        ttk.Button(f, text='Reload Data', command=self.load_initial_data).grid(row=2, column=1, sticky='e', **pad)

    def create_analysis_tab(self):
        f = self.analysis_frame
        pad = {'padx': 10, 'pady': 5}
        # Controls
        self.overlay_var = tk.BooleanVar(value=True)
        self.hcv_var = tk.BooleanVar(value=True)
        self.hole_var = tk.BooleanVar(value=False)
        self.area_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(f, text='Spatial Overlay Analysis', variable=self.overlay_var).grid(row=0, column=0, sticky='w', **pad)
        ttk.Checkbutton(f, text='HCV Category Processing', variable=self.hcv_var).grid(row=1, column=0, sticky='w', **pad)
        ttk.Checkbutton(f, text='Polygon Hole Creation', variable=self.hole_var).grid(row=2, column=0, sticky='w', **pad)
        ttk.Checkbutton(f, text='Area Calculation', variable=self.area_var).grid(row=3, column=0, sticky='w', **pad)
        # Start/cancel
        self.start_btn = ttk.Button(f, text='Run Analysis', command=self.start_analysis)
        self.start_btn.grid(row=4, column=0, sticky='w', **pad)
        self.cancel_btn = ttk.Button(f, text='Cancel', command=self.cancel_analysis, state='disabled')
        self.cancel_btn.grid(row=4, column=1, sticky='w', **pad)
        # Progress
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(f, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=5, column=0, columnspan=2, sticky='ew', **pad)
        self.status_var = tk.StringVar(value='Ready')
        ttk.Label(f, textvariable=self.status_var).grid(row=6, column=0, columnspan=2, sticky='w', **pad)

    def create_results_tab(self):
        f = self.results_frame
        pad = {'padx': 10, 'pady': 5}
        # Table
        columns = ('BLOK', 'SUBDIVISI', 'HCV', 'JUMLAH_POH', 'luas_total', 'luas_inclave', 'luas_netto')
        self.tree = ttk.Treeview(f, columns=columns, show='headings', height=20)
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120)
        self.tree.grid(row=0, column=0, columnspan=4, sticky='nsew', **pad)
        # Scrollbar
        vsb = ttk.Scrollbar(f, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=vsb.set)
        vsb.grid(row=0, column=4, sticky='ns')
        # Export
        ttk.Button(f, text='Export to Excel', command=self.export_excel).grid(row=1, column=0, **pad)
        ttk.Button(f, text='Export to Shapefile', command=self.export_shapefile).grid(row=1, column=1, **pad)

    def create_log_tab(self):
        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=30, state='disabled')
        self.log_text.pack(fill='both', expand=True)

    def browse_boundary(self):
        path = filedialog.askopenfilename(filetypes=[('Shapefile', '*.shp')])
        if path:
            self.boundary_path = path
            self.boundary_entry.delete(0, 'end')
            self.boundary_entry.insert(0, path)

    def browse_points(self):
        path = filedialog.askopenfilename(filetypes=[('Shapefile', '*.shp')])
        if path:
            self.points_path = path
            self.points_entry.delete(0, 'end')
            self.points_entry.insert(0, path)

    def load_initial_data(self):
        self.log('Loading data...')
        try:
            if not os.path.exists(self.boundary_path):
                self.log(f'Boundary file not found: {self.boundary_path}')
                return
            if not os.path.exists(self.points_path):
                self.log(f'Points file not found: {self.points_path}')
                return
            self.gdf_boundary = gpd.read_file(self.boundary_path)
            self.gdf_points = gpd.read_file(self.points_path)
            self.log(f'Loaded {len(self.gdf_boundary)} polygons, {len(self.gdf_points)} points')
            self.status_var.set('Data loaded')
        except Exception as e:
            self.log(f'Error loading data: {e}')
            self.status_var.set('Error loading data')

    def start_analysis(self):
        if self.processing:
            return
        self.processing = True
        self.cancel_flag = False
        self.start_btn.config(state='disabled')
        self.cancel_btn.config(state='normal')
        t = threading.Thread(target=self.run_analysis)
        t.daemon = True
        t.start()

    def cancel_analysis(self):
        self.cancel_flag = True
        self.status_var.set('Cancelling...')
        self.log('Analysis cancelled by user.')

    def run_analysis(self):
        try:
            self.status_var.set('Running analysis...')
            self.progress_var.set(0)
            self.log('Starting spatial analysis...')
            gdf = self.gdf_boundary.copy()
            points = self.gdf_points.copy()
            # CRS
            if gdf.crs != points.crs:
                self.log('CRS mismatch, reprojecting points...')
                points = points.to_crs(gdf.crs)
            # --- Overlay Analysis ---
            if self.overlay_var.get():
                self.progress_var.set(10)
                self.log('Performing spatial overlay (point-in-polygon)...')
                join = gpd.sjoin(points, gdf, how='inner', predicate='within')
                pohon_count = join.groupby(['BLOK','SUBDIVISI']).size().reset_index(name='JUMLAH_POH')
            else:
                pohon_count = pd.DataFrame(columns=['BLOK','SUBDIVISI','JUMLAH_POH'])
            # --- HCV & Area ---
            if self.hcv_var.get() or self.area_var.get():
                self.progress_var.set(30)
                self.log('Processing HCV and area calculations...')
                # HCV 0 = boundary, HCV 1 = enclave/unplanted
                gdf['HCV'] = gdf['HCV'].astype(str)
                gdf0 = gdf[gdf['HCV'].isin(['0', 0])].copy()
                gdf1 = gdf[gdf['HCV'].isin(['1', 1])].copy()
                # Area (hektar)
                if not gdf0.crs or not gdf0.crs.is_projected:
                    gdf0 = gdf0.to_crs(epsg=32748)
                gdf0['luas_total'] = gdf0.geometry.area / 10000
                gdf1['luas_inclave'] = gdf1.geometry.area / 10000
                # Group by blok/subdivisi
                luas_total = gdf0.groupby(['BLOK','SUBDIVISI'])['luas_total'].sum().reset_index()
                luas_inclave = gdf1.groupby(['BLOK','SUBDIVISI'])['luas_inclave'].sum().reset_index()
                df_sum = pd.merge(luas_total, luas_inclave, on=['BLOK','SUBDIVISI'], how='left').fillna({'luas_inclave':0})
                df_sum['luas_netto'] = df_sum['luas_total'] - df_sum['luas_inclave']
            else:
                df_sum = pd.DataFrame(columns=['BLOK','SUBDIVISI','luas_total','luas_inclave','luas_netto'])
            # --- Gabung pohon ke summary ---
            df_sum = pd.merge(df_sum, pohon_count, on=['BLOK','SUBDIVISI'], how='left').fillna({'JUMLAH_POH':0})
            # --- Update boundary fields ---
            for idx, row in df_sum.iterrows():
                mask = (gdf['BLOK']==row['BLOK']) & (gdf['SUBDIVISI']==row['SUBDIVISI']) & (gdf['HCV'].isin(['0', 0]))
                gdf.loc[mask, 'luas_total'] = row['luas_total']
                gdf.loc[mask, 'luas_inclave'] = row['luas_inclave']
                gdf.loc[mask, 'luas_netto'] = row['luas_netto']
                gdf.loc[mask, 'JUMLAH_POH'] = int(row['JUMLAH_POH'])
            # --- Polygon Hole Creation ---
            if self.hole_var.get():
                self.progress_var.set(70)
                self.log('Creating polygon holes (advanced)...')
                gdf = self.create_polygon_holes(gdf)
            # --- Simpan hasil ---
            self.processed_gdf = gdf
            self.df_summary = df_sum
            self.update_results_table()
            self.progress_var.set(100)
            self.status_var.set('Analysis complete')
            self.log('Analysis complete.')
        except Exception as e:
            self.log(f'Error during analysis: {e}')
            self.status_var.set('Error during analysis')
        finally:
            self.processing = False
            self.start_btn.config(state='normal')
            self.cancel_btn.config(state='disabled')

    def create_polygon_holes(self, gdf):
        # Buat holes: untuk setiap boundary (HCV=0), cari enclave (HCV=1) yang contained
        try:
            gdf = gdf.copy()
            gdf['geometry'] = gdf['geometry'].buffer(0) # fix invalid
            for (blok, subdiv), group in gdf.groupby(['BLOK','SUBDIVISI']):
                boundary = group[(group['HCV'].isin(['0', 0]))]
                enclaves = group[(group['HCV'].isin(['1', 1]))]
                if len(boundary)==0 or len(enclaves)==0:
                    continue
                # Gabung semua enclave jadi satu multipolygon
                enclaves_union = unary_union(enclaves.geometry)
                for idx, row in boundary.iterrows():
                    geom = row.geometry
                    if enclaves_union.is_empty:
                        continue
                    # Buat hole jika enclave contained
                    if geom.contains(enclaves_union):
                        new_geom = Polygon(geom.exterior.coords, [p.exterior.coords for p in getattr(enclaves_union, 'geoms', [enclaves_union])])
                        gdf.at[idx, 'geometry'] = new_geom
            return gdf
        except Exception as e:
            self.log(f'Error in hole creation: {e}')
            return gdf

    def update_results_table(self):
        self.tree.delete(*self.tree.get_children())
        if self.df_summary is not None:
            for _, row in self.df_summary.iterrows():
                self.tree.insert('', 'end', values=(row['BLOK'], row['SUBDIVISI'], '', int(row['JUMLAH_POH']), f"{row['luas_total']:.2f}", f"{row['luas_inclave']:.2f}", f"{row['luas_netto']:.2f}"))

    def export_excel(self):
        if self.df_summary is None:
            messagebox.showerror('Error', 'No summary to export!')
            return
        path = filedialog.asksaveasfilename(defaultextension='.xlsx', filetypes=[('Excel','*.xlsx')])
        if path:
            self.df_summary.to_excel(path, index=False)
            self.log(f'Exported summary to {path}')

    def export_shapefile(self):
        if self.processed_gdf is None:
            messagebox.showerror('Error', 'No processed data to export!')
            return
        path = filedialog.asksaveasfilename(defaultextension='.shp', filetypes=[('Shapefile','*.shp')])
        if path:
            self.processed_gdf.to_file(path)
            self.log(f'Exported shapefile to {path}')

if __name__ == '__main__':
    root = tk.Tk()
    app = ARECPalmTreeAdvancedGUI(root)
    root.mainloop() 
"""
Test script to verify data format compatibility with ARE C Advanced Spatial Analyzer
This script checks the actual data structure and validates compatibility
"""

import geopandas as gpd
import pandas as pd
import os

def test_boundary_data():
    """Test boundary shapefile structure"""
    print("Testing Boundary Shapefile...")
    print("=" * 40)
    
    boundary_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
    
    if not os.path.exists(boundary_path):
        print(f"❌ Boundary file not found: {boundary_path}")
        return False
    
    try:
        gdf = gpd.read_file(boundary_path)
        print(f"✅ Successfully loaded {len(gdf)} boundary polygons")
        print(f"CRS: {gdf.crs}")
        print(f"Columns: {list(gdf.columns)}")
        
        # Check for required columns after standardization
        required_cols = ['BLOK', 'SUB_DIVISI', 'HCV_Catego']
        missing_cols = [col for col in required_cols if col not in gdf.columns]
        
        if missing_cols:
            print(f"❌ Missing columns: {missing_cols}")
            return False
        else:
            print("✅ All required columns present")
        
        # Analyze HCV categories
        print(f"\nHCV Categories Analysis:")
        hcv_values = gdf['HCV_Catego'].unique()
        print(f"Unique HCV values: {hcv_values}")
        
        boundary_count = len(gdf[gdf['HCV_Catego'].astype(str).str.contains('Boundary', na=False)])
        inclave_count = len(gdf[gdf['HCV_Catego'].astype(str).str.contains('inclave', case=False, na=False)])
        
        print(f"Boundary polygons: {boundary_count}")
        print(f"Inclave polygons: {inclave_count}")
        
        # Check existing tree count data
        if 'Jumlah_Poh' in gdf.columns:
            existing_trees = gdf['Jumlah_Poh'].sum()
            print(f"Existing tree count: {existing_trees}")
        
        # Sample data
        print(f"\nSample data (first 3 rows):")
        sample_cols = ['BLOK', 'SUB_DIVISI', 'HCV_Catego', 'LUAS_AUTO']
        if 'Jumlah_Poh' in gdf.columns:
            sample_cols.append('Jumlah_Poh')
        
        print(gdf[sample_cols].head(3).to_string())
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading boundary data: {e}")
        return False

def test_points_data():
    """Test detection points shapefile structure"""
    print("\n\nTesting Detection Points Shapefile...")
    print("=" * 40)
    
    points_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"
    
    if not os.path.exists(points_path):
        print(f"❌ Points file not found: {points_path}")
        return False
    
    try:
        gdf = gpd.read_file(points_path)
        print(f"✅ Successfully loaded {len(gdf)} detection points")
        print(f"CRS: {gdf.crs}")
        print(f"Columns: {list(gdf.columns)}")
        
        # Check for geometry
        if 'geometry' not in gdf.columns:
            print("❌ No geometry column found")
            return False
        else:
            print("✅ Geometry column present")
        
        # Check for confidence scores
        if 'confidence' in gdf.columns:
            conf_stats = gdf['confidence'].describe()
            print(f"\nConfidence statistics:")
            print(f"  Min: {conf_stats['min']:.3f}")
            print(f"  Max: {conf_stats['max']:.3f}")
            print(f"  Mean: {conf_stats['mean']:.3f}")
        
        # Sample data
        print(f"\nSample data (first 3 rows):")
        sample_cols = ['confidence', 'geo_x', 'geo_y']
        if 'BLOCK' in gdf.columns:
            sample_cols.append('BLOCK')
        if 'SUBDIVISIO' in gdf.columns:
            sample_cols.append('SUBDIVISIO')
        
        available_cols = [col for col in sample_cols if col in gdf.columns]
        print(gdf[available_cols].head(3).to_string())
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading points data: {e}")
        return False

def test_column_mapping():
    """Test column name mapping logic"""
    print("\n\nTesting Column Name Mapping...")
    print("=" * 40)
    
    # Test boundary mappings
    boundary_mappings = {
        'SUB_DIVISI': 'SUBDIVISI',
        'HCV_Catego': 'HCV',
        'Jumlah_Poh': 'JUMLAH_POH'
    }
    
    print("Boundary column mappings:")
    for old_name, new_name in boundary_mappings.items():
        print(f"  {old_name} → {new_name}")
    
    # Test points mappings
    points_mappings = {
        'SUBDIVISIO': 'SUBDIVISI',
        'BLOCK': 'BLOK'
    }
    
    print("\nPoints column mappings:")
    for old_name, new_name in points_mappings.items():
        print(f"  {old_name} → {new_name}")
    
    return True

def test_hcv_pattern_matching():
    """Test HCV pattern matching logic"""
    print("\n\nTesting HCV Pattern Matching...")
    print("=" * 40)
    
    # Sample HCV values from your data
    sample_hcv_values = [
        "Boundary-29", "Boundary-45", "Boundary-11", 
        "Boundary-43", "Boundary-49", "Boundary-47"
    ]
    
    print("Testing boundary pattern matching:")
    for hcv_value in sample_hcv_values:
        is_boundary = 'Boundary' in str(hcv_value)
        is_inclave = 'inclave' in str(hcv_value).lower()
        print(f"  '{hcv_value}' → Boundary: {is_boundary}, Inclave: {is_inclave}")
    
    return True

def main():
    """Run all data format tests"""
    print("ARE C Data Format Compatibility Test")
    print("=" * 50)
    
    tests = [
        ("Boundary Data Test", test_boundary_data),
        ("Points Data Test", test_points_data),
        ("Column Mapping Test", test_column_mapping),
        ("HCV Pattern Matching Test", test_hcv_pattern_matching)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your data is compatible with the analyzer.")
        print("You can now run the main application:")
        print("  python are_c_advanced_spatial_analyzer.py")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the data format.")

if __name__ == "__main__":
    main()

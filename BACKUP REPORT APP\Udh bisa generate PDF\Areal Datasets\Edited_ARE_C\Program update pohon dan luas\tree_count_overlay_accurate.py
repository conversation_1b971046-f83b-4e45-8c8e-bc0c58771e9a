import geopandas as gpd
import pandas as pd
import os
from shapely.geometry import Point

def count_trees_with_overlay(polygon_path, points_path, output_path=None):
    """
    Menghitung jumlah pohon dalam setiap polygon menggunakan spatial overlay
    
    Parameters:
    - polygon_path: Path ke shapefile polygon (cleaned)
    - points_path: Path ke shapefile titik deteksi pohon
    - output_path: Path untuk menyimpan hasil
    
    Returns:
    - GeoDataFrame dengan hasil penghitungan baru
    """
    
    print("=== PENGHITUNGAN POHON DENGAN SPATIAL OVERLAY ===")
    print(f"Polygon file: {polygon_path}")
    print(f"Points file: {points_path}")
    
    # 1. Baca shapefile polygon
    try:
        polygons = gpd.read_file(polygon_path)
        print(f"✓ Berhasil membaca {len(polygons)} polygon")
        print(f"Kolom polygon: {list(polygons.columns)}")
    except Exception as e:
        print(f"Error membaca polygon: {e}")
        return None
    
    # 2. Baca shapefile titik deteksi pohon
    try:
        points = gpd.read_file(points_path)
        print(f"✓ Berhasil membaca {len(points)} titik deteksi pohon")
        print(f"Kolom points: {list(points.columns)}")
    except Exception as e:
        print(f"Error membaca points: {e}")
        return None
    
    # 3. Pastikan CRS sama
    if polygons.crs != points.crs:
        print(f"Menyamakan CRS: {points.crs} -> {polygons.crs}")
        points = points.to_crs(polygons.crs)
    else:
        print(f"✓ CRS sudah sama: {polygons.crs}")
    
    # 4. Lakukan spatial overlay (point in polygon)
    print("\nMelakukan spatial overlay...")
    print("Metode: sjoin dengan predicate 'within' (titik di dalam polygon)")
    
    # Spatial join untuk mendapatkan titik yang berada dalam polygon
    overlay_result = gpd.sjoin(points, polygons, how='inner', predicate='within')
    
    print(f"✓ Ditemukan {len(overlay_result)} titik pohon yang berada dalam polygon")
    
    # 5. Hitung jumlah pohon per polygon
    print("Menghitung jumlah pohon per polygon...")
    
    # Group by index polygon (index_right dari hasil sjoin)
    tree_counts = overlay_result.groupby('index_right').size().reset_index(name='JUMLAH_POH_BARU')
    
    # 6. Merge hasil dengan polygon asli
    polygons_result = polygons.copy()
    polygons_result['poly_index'] = polygons_result.index
    
    # Merge dengan hasil penghitungan
    polygons_result = polygons_result.merge(
        tree_counts, 
        left_on='poly_index', 
        right_on='index_right', 
        how='left'
    )
    
    # Isi nilai kosong dengan 0 (polygon yang tidak ada pohonnya)
    polygons_result['JUMLAH_POH_BARU'] = polygons_result['JUMLAH_POH_BARU'].fillna(0).astype(int)
    
    # Buat perbandingan dengan nilai lama (jika ada)
    if 'JUMLAH_POH' in polygons_result.columns:
        polygons_result['SELISIH'] = polygons_result['JUMLAH_POH_BARU'] - polygons_result['JUMLAH_POH']
        print(f"\n=== PERBANDINGAN HASIL ===")
        print("BLOK | Lama | Baru | Selisih")
        print("-" * 35)
        for idx, row in polygons_result.iterrows():
            blok = row.get('BLOK', f'Poly_{idx}')
            lama = row.get('JUMLAH_POH', 0)
            baru = row['JUMLAH_POH_BARU']
            selisih = row['SELISIH']
            print(f"{blok:<8} | {lama:>4} | {baru:>4} | {selisih:>+4}")
    
    # Update kolom JUMLAH_POH dengan hasil baru
    polygons_result['JUMLAH_POH'] = polygons_result['JUMLAH_POH_BARU']
    
    # Hapus kolom sementara
    polygons_result = polygons_result.drop(['poly_index', 'index_right', 'JUMLAH_POH_BARU'], axis=1, errors='ignore')
    if 'SELISIH' in polygons_result.columns:
        polygons_result = polygons_result.drop('SELISIH', axis=1)
    
    # 7. Tampilkan hasil final
    print(f"\n=== HASIL PENGHITUNGAN OVERLAY ===")
    total_pohon = polygons_result['JUMLAH_POH'].sum()
    
    if 'BLOK' in polygons_result.columns:
        print("Hasil per BLOK:")
        for idx, row in polygons_result.iterrows():
            blok = row['BLOK']
            jumlah = row['JUMLAH_POH']
            luas = row.get('luas_auto', 'N/A')
            print(f"  {blok}: {jumlah} pohon (luas: {luas})")
    
    print(f"\nTotal pohon keseluruhan: {total_pohon}")
    print(f"Rata-rata pohon per polygon: {total_pohon / len(polygons_result):.2f}")
    
    # 8. Simpan hasil
    if output_path:
        try:
            polygons_result.to_file(output_path)
            print(f"✓ Hasil disimpan ke: {output_path}")
            
            # Simpan juga detail overlay untuk referensi
            detail_path = output_path.replace('.shp', '_detail_overlay.csv')
            overlay_detail = overlay_result[['index_right']].copy()
            overlay_detail['polygon_id'] = overlay_detail['index_right']
            overlay_detail.to_csv(detail_path, index=False)
            print(f"✓ Detail overlay disimpan ke: {detail_path}")
            
        except Exception as e:
            print(f"Error menyimpan: {e}")
    
    return polygons_result

def main():
    """
    Fungsi utama untuk menjalankan penghitungan overlay
    """
    
    print("🌳 TREE COUNTING - SPATIAL OVERLAY METHOD 🌳")
    print("=" * 55)
    
    # Path file
    polygon_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Polygon_ARE_C_dengan_Jumlah_Pohon_cleaned.shp"
    
    points_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"
    
    output_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Polygon_ARE_C_TreeCount_Overlay.shp"
    
    # Periksa keberadaan file
    if not os.path.exists(polygon_path):
        print(f"❌ Error: File polygon tidak ditemukan: {polygon_path}")
        return
    
    if not os.path.exists(points_path):
        print(f"❌ Error: File points tidak ditemukan: {points_path}")
        return
    
    print(f"Input polygon: {os.path.basename(polygon_path)}")
    print(f"Input points: {os.path.basename(points_path)}")
    print(f"Output: {os.path.basename(output_path)}")
    print()
    
    # Jalankan analisis
    result = count_trees_with_overlay(polygon_path, points_path, output_path)
    
    if result is not None:
        print(f"\n✅ PENGHITUNGAN OVERLAY SELESAI!")
        print(f"File hasil: {output_path}")
        print("Siap untuk analisis lebih lanjut atau pembuatan laporan.")
    else:
        print(f"\n❌ PENGHITUNGAN GAGAL!")

if __name__ == "__main__":
    main() 
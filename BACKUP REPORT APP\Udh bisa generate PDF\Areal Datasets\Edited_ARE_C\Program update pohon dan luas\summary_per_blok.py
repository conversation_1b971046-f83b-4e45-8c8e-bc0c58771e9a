import geopandas as gpd
import pandas as pd
import os

shp_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Polygon_ARE_C_dengan_Jumlah_Pohon_cleaned.shp"

# Baca shapefile
print(f"Membaca file: {shp_path}")
gdf = gpd.read_file(shp_path)

# Pastikan kolom yang diperlukan ada
def check_columns(gdf, cols):
    for col in cols:
        if col not in gdf.columns:
            raise ValueError(f"Kolom '{col}' tidak ditemukan di shapefile!")

check_columns(gdf, ['BLOK', 'luas_auto', 'JUMLAH_POH'])

# Pastikan luas_auto dan JUMLAH_POH bertipe numerik
gdf['luas_auto'] = pd.to_numeric(gdf['luas_auto'], errors='coerce')
gdf['JUMLAH_POH'] = pd.to_numeric(gdf['JUMLAH_POH'], errors='coerce')

# Group by BLOK dan summary
grouped = gdf.groupby('BLOK').agg(
    total_luas = ('luas_auto', 'sum'),
    total_pohon = ('JUMLAH_POH', 'sum'),
    jumlah_polygon = ('BLOK', 'count')
).reset_index()

print("\nSUMMARY PER BLOK:")
print(grouped)

# Simpan ke Excel dan CSV
excel_path = shp_path.replace('.shp', '_summary.xlsx')
csv_path = shp_path.replace('.shp', '_summary.csv')

# Penjelasan/uraian
urai = [
    ["Uraian"],
    ["File ini berisi rekapitulasi total luas dan jumlah pohon untuk setiap BLOK."],
    ["Kolom:"],
    ["- BLOK: Nama blok"],
    ["- total_luas: Total luas seluruh polygon pada blok (penjumlahan kolom luas_auto)"],
    ["- total_pohon: Total jumlah pohon pada blok (penjumlahan kolom JUMLAH_POH)"],
    ["- jumlah_polygon: Banyaknya polygon unik pada blok"],
    [""],
    ["Jika file tidak bisa dibuka, pastikan tidak sedang dibuka aplikasi lain dan library openpyxl sudah terinstall."]
]

try:
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        grouped.to_excel(writer, index=False, sheet_name='Summary')
        pd.DataFrame(urai).to_excel(writer, index=False, header=False, sheet_name='Uraian')
    print(f"\nSummary disimpan ke: {excel_path}")
except Exception as e:
    print(f"Gagal menyimpan ke Excel: {e}")
    print("Coba install openpyxl dengan: pip install openpyxl")
    print("Menyimpan ke CSV saja...")
    grouped.to_csv(csv_path, index=False)
    print(f"Summary disimpan ke: {csv_path}") 
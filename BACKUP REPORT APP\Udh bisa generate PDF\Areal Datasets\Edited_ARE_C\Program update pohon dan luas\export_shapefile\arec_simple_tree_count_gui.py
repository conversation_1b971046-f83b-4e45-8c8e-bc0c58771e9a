"""
ARE C Simple Tree Count GUI
GUI sederhana untuk menghitung total pohon dan luas per blok boundary
Fokus pada: 
- Spatial overlay (point-in-polygon)
- Total pohon per blok
- Luas total per blok
- Tanpa fitur hole creation yang kompleks

Author: Generated for ARE C Analysis
Date: 2025-01-03
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import geopandas as gpd
import pandas as pd
import os
import threading

# Default paths
DEFAULT_BOUNDARY_PATH = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
DEFAULT_POINTS_PATH = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp"

class ARECSimpleTreeCountGUI:
    def __init__(self, root):
        self.root = root
        self.root.title('ARE C Simple Tree Count & Area Calculator')
        self.root.geometry('1400x800')
        
        # Data
        self.boundary_path = DEFAULT_BOUNDARY_PATH
        self.points_path = DEFAULT_POINTS_PATH
        self.gdf_boundary = None
        self.gdf_points = None
        self.results_df = None
        self.processing = False
        
        self.create_gui()
        self.load_data()

    def create_gui(self):
        """Create GUI interface"""
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # File paths section
        path_frame = ttk.LabelFrame(main_frame, text='File Paths', padding=10)
        path_frame.pack(fill='x', pady=5)
        
        # Boundary file
        ttk.Label(path_frame, text='Boundary Shapefile:').grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.boundary_entry = ttk.Entry(path_frame, width=80)
        self.boundary_entry.grid(row=0, column=1, padx=5, pady=2)
        self.boundary_entry.insert(0, self.boundary_path)
        ttk.Button(path_frame, text='Browse', command=self.browse_boundary).grid(row=0, column=2, padx=5, pady=2)
        
        # Points file
        ttk.Label(path_frame, text='Detection Points:').grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.points_entry = ttk.Entry(path_frame, width=80)
        self.points_entry.grid(row=1, column=1, padx=5, pady=2)
        self.points_entry.insert(0, self.points_path)
        ttk.Button(path_frame, text='Browse', command=self.browse_points).grid(row=1, column=2, padx=5, pady=2)
        
        # Control buttons
        control_frame = ttk.Frame(path_frame)
        control_frame.grid(row=2, column=1, pady=10)
        
        ttk.Button(control_frame, text='Load Data', command=self.load_data).pack(side='left', padx=5)
        ttk.Button(control_frame, text='Analyze All Blocks', command=self.start_analysis).pack(side='left', padx=5)
        ttk.Button(control_frame, text='Export Results', command=self.export_results).pack(side='left', padx=5)
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text='Analysis Results - Tree Count & Area per Block', padding=10)
        results_frame.pack(fill='both', expand=True, pady=5)
        
        # Results table
        columns = ('BLOK', 'TOTAL_POHON', 'LUAS_HA', 'KEPADATAN_POHON_PER_HA', 'JUMLAH_FITUR')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_configs = {
            'BLOK': ('Block', 100),
            'TOTAL_POHON': ('Total Trees', 120),
            'LUAS_HA': ('Area (Ha)', 120),
            'KEPADATAN_POHON_PER_HA': ('Trees/Ha', 140),
            'JUMLAH_FITUR': ('Features', 100)
        }
        
        for col, (header, width) in column_configs.items():
            self.results_tree.heading(col, text=header)
            self.results_tree.column(col, width=width, anchor='center')
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(results_frame, orient='vertical', command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(results_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.results_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        
        # Summary section
        summary_frame = ttk.LabelFrame(main_frame, text='Summary Statistics', padding=10)
        summary_frame.pack(fill='x', pady=5)
        
        self.summary_text = scrolledtext.ScrolledText(summary_frame, height=6)
        self.summary_text.pack(fill='x')
        
        # Log section
        log_frame = ttk.LabelFrame(main_frame, text='Processing Log', padding=10)
        log_frame.pack(fill='x', pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, state='disabled')
        self.log_text.pack(fill='x')
        
        # Status
        self.status_var = tk.StringVar(value='Ready')
        ttk.Label(main_frame, textvariable=self.status_var).pack(pady=2)

    def log(self, message):
        """Add message to log"""
        self.log_text.configure(state='normal')
        self.log_text.insert('end', message + '\n')
        self.log_text.see('end')
        self.log_text.configure(state='disabled')
        print(message)  # Also print to console

    def browse_boundary(self):
        """Browse for boundary file"""
        path = filedialog.askopenfilename(filetypes=[('Shapefile', '*.shp')])
        if path:
            self.boundary_path = path
            self.boundary_entry.delete(0, 'end')
            self.boundary_entry.insert(0, path)

    def browse_points(self):
        """Browse for points file"""
        path = filedialog.askopenfilename(filetypes=[('Shapefile', '*.shp')])
        if path:
            self.points_path = path
            self.points_entry.delete(0, 'end')
            self.points_entry.insert(0, path)

    def load_data(self):
        """Load shapefile data"""
        self.log("Loading data...")
        self.status_var.set('Loading data...')
        
        try:
            # Get paths from entries
            self.boundary_path = self.boundary_entry.get()
            self.points_path = self.points_entry.get()
            
            # Check files exist
            if not os.path.exists(self.boundary_path):
                self.log(f"ERROR: Boundary file not found: {self.boundary_path}")
                return
            
            if not os.path.exists(self.points_path):
                self.log(f"ERROR: Points file not found: {self.points_path}")
                return
            
            # Load boundary
            self.log("Loading boundary polygons...")
            self.gdf_boundary = gpd.read_file(self.boundary_path)
            self.log(f"✓ Loaded {len(self.gdf_boundary)} boundary polygons")
            
            # Standardize column names
            column_mappings = {
                'SUB_DIVISI': 'SUBDIVISI',
                'HCV_Catego': 'HCV',
                'Jumlah_Poh': 'JUMLAH_POH'
            }
            
            for old, new in column_mappings.items():
                if old in self.gdf_boundary.columns and new not in self.gdf_boundary.columns:
                    self.gdf_boundary = self.gdf_boundary.rename(columns={old: new})
                    self.log(f"Renamed column: {old} → {new}")
            
            # Load points
            self.log("Loading detection points...")
            self.gdf_points = gpd.read_file(self.points_path)
            self.log(f"✓ Loaded {len(self.gdf_points)} detection points")
            
            # Show data info
            self.log(f"Boundary CRS: {self.gdf_boundary.crs}")
            self.log(f"Points CRS: {self.gdf_points.crs}")
            self.log(f"Boundary columns: {list(self.gdf_boundary.columns)}")
            
            # Analyze blocks
            if 'BLOK' in self.gdf_boundary.columns:
                unique_blocks = sorted(self.gdf_boundary['BLOK'].unique())
                self.log(f"Found {len(unique_blocks)} unique blocks: {unique_blocks}")
            else:
                self.log("WARNING: No BLOK column found in boundary data")
            
            self.status_var.set('Data loaded successfully')
            
        except Exception as e:
            error_msg = f"Error loading data: {str(e)}"
            self.log(error_msg)
            self.status_var.set('Error loading data')
            messagebox.showerror("Error", error_msg)

    def start_analysis(self):
        """Start analysis in separate thread"""
        if self.processing:
            return
        
        if self.gdf_boundary is None or self.gdf_points is None:
            messagebox.showerror("Error", "Please load data first!")
            return
        
        self.processing = True
        thread = threading.Thread(target=self.run_analysis)
        thread.daemon = True
        thread.start()

    def run_analysis(self):
        """Run the tree counting and area analysis"""
        try:
            self.status_var.set('Running analysis...')
            self.log("=== STARTING TREE COUNT & AREA ANALYSIS ===")
            
            # Copy data
            gdf_boundary = self.gdf_boundary.copy()
            gdf_points = self.gdf_points.copy()
            
            # Ensure CRS compatibility
            if gdf_boundary.crs != gdf_points.crs:
                self.log(f"Converting points CRS: {gdf_points.crs} → {gdf_boundary.crs}")
                gdf_points = gdf_points.to_crs(gdf_boundary.crs)
            
            # Convert to projected CRS for area calculation
            if not gdf_boundary.crs or not gdf_boundary.crs.is_projected:
                self.log("Converting to projected CRS for area calculation...")
                gdf_boundary = gdf_boundary.to_crs(epsg=32748)  # UTM Zone 48S
                gdf_points = gdf_points.to_crs(epsg=32748)
            
            # Calculate area for all polygons
            self.log("Calculating polygon areas...")
            gdf_boundary['area_sqm'] = gdf_boundary.geometry.area
            gdf_boundary['area_ha'] = gdf_boundary['area_sqm'] / 10000
            
            # Perform spatial overlay (all boundary polygons vs points)
            self.log("Performing spatial overlay (point-in-polygon)...")
            overlay_result = gpd.sjoin(gdf_points, gdf_boundary, how='inner', predicate='within')
            self.log(f"Found {len(overlay_result)} points within boundary polygons")
            
            # Group results by BLOK
            if 'BLOK' in gdf_boundary.columns:
                self.log("Calculating results per block...")
                
                # Count trees per block
                tree_counts = overlay_result.groupby('BLOK').size().reset_index(name='total_trees')
                
                # Calculate area per block
                area_per_block = gdf_boundary.groupby('BLOK').agg({
                    'area_ha': 'sum',
                    'BLOK': 'size'  # Count features per block
                }).rename(columns={'BLOK': 'feature_count'}).reset_index()
                
                # Merge results
                results = pd.merge(area_per_block, tree_counts, on='BLOK', how='left')
                results['total_trees'] = results['total_trees'].fillna(0).astype(int)
                
                # Calculate tree density
                results['tree_density'] = results.apply(
                    lambda row: row['total_trees'] / row['area_ha'] if row['area_ha'] > 0 else 0, 
                    axis=1
                )
                
                # Sort by block name
                results = results.sort_values('BLOK')
                
                # Store results
                self.results_df = results
                
                # Update GUI
                self.update_results_table()
                self.update_summary()
                
                # Log results
                self.log("\nResults per block:")
                for _, row in results.iterrows():
                    self.log(f"  {row['BLOK']}: {row['total_trees']:,} trees, {row['area_ha']:.2f} ha, "
                           f"{row['tree_density']:.2f} trees/ha")
                
                total_trees = results['total_trees'].sum()
                total_area = results['area_ha'].sum()
                overall_density = total_trees / total_area if total_area > 0 else 0
                
                self.log(f"\nOVERALL TOTALS:")
                self.log(f"  Total trees: {total_trees:,}")
                self.log(f"  Total area: {total_area:,.2f} ha")
                self.log(f"  Overall density: {overall_density:.2f} trees/ha")
                
            else:
                self.log("ERROR: No BLOK column found for grouping results")
                
            self.status_var.set('Analysis completed')
            self.log("=== ANALYSIS COMPLETED ===")
            
        except Exception as e:
            error_msg = f"Error during analysis: {str(e)}"
            self.log(error_msg)
            self.status_var.set('Analysis failed')
            import traceback
            self.log(traceback.format_exc())
            
        finally:
            self.processing = False

    def update_results_table(self):
        """Update the results table"""
        # Clear existing data
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        if self.results_df is not None:
            for _, row in self.results_df.iterrows():
                values = (
                    row['BLOK'],
                    f"{row['total_trees']:,}",
                    f"{row['area_ha']:.2f}",
                    f"{row['tree_density']:.2f}",
                    int(row['feature_count'])
                )
                self.results_tree.insert('', 'end', values=values)

    def update_summary(self):
        """Update summary statistics"""
        if self.results_df is None:
            return
        
        # Calculate summary statistics
        total_trees = self.results_df['total_trees'].sum()
        total_area = self.results_df['area_ha'].sum()
        overall_density = total_trees / total_area if total_area > 0 else 0
        
        num_blocks = len(self.results_df)
        avg_trees_per_block = self.results_df['total_trees'].mean()
        avg_area_per_block = self.results_df['area_ha'].mean()
        
        # Blocks with highest/lowest tree counts
        max_trees_block = self.results_df.loc[self.results_df['total_trees'].idxmax()]
        min_trees_block = self.results_df.loc[self.results_df['total_trees'].idxmin()]
        
        # Blocks with highest/lowest density
        max_density_block = self.results_df.loc[self.results_df['tree_density'].idxmax()]
        min_density_block = self.results_df.loc[self.results_df['tree_density'].idxmin()]
        
        summary_text = f"""=== SUMMARY STATISTICS ===

OVERALL TOTALS:
• Total Trees Detected: {total_trees:,}
• Total Area: {total_area:,.2f} hectares
• Overall Tree Density: {overall_density:.2f} trees/hectare
• Number of Blocks: {num_blocks}

AVERAGES:
• Average Trees per Block: {avg_trees_per_block:.0f}
• Average Area per Block: {avg_area_per_block:.2f} ha

HIGHEST/LOWEST TREE COUNTS:
• Highest: {max_trees_block['BLOK']} ({max_trees_block['total_trees']:,} trees)
• Lowest: {min_trees_block['BLOK']} ({min_trees_block['total_trees']:,} trees)

HIGHEST/LOWEST TREE DENSITY:
• Highest: {max_density_block['BLOK']} ({max_density_block['tree_density']:.2f} trees/ha)
• Lowest: {min_density_block['BLOK']} ({min_density_block['tree_density']:.2f} trees/ha)
"""
        
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(1.0, summary_text)

    def export_results(self):
        """Export results to file"""
        if self.results_df is None:
            messagebox.showerror("Error", "No results to export!")
            return
        
        # Ask for export format
        export_format = messagebox.askyesnocancel(
            "Export Format", 
            "Choose export format:\nYes = Excel (.xlsx)\nNo = CSV (.csv)\nCancel = Cancel"
        )
        
        if export_format is None:  # Cancel
            return
        
        if export_format:  # Excel
            filename = filedialog.asksaveasfilename(
                defaultextension='.xlsx',
                filetypes=[('Excel files', '*.xlsx'), ('All files', '*.*')]
            )
            if filename:
                try:
                    self.results_df.to_excel(filename, index=False)
                    self.log(f"Results exported to Excel: {filename}")
                    messagebox.showinfo("Success", f"Results exported to:\n{filename}")
                except Exception as e:
                    messagebox.showerror("Error", f"Export failed: {str(e)}")
        
        else:  # CSV
            filename = filedialog.asksaveasfilename(
                defaultextension='.csv',
                filetypes=[('CSV files', '*.csv'), ('All files', '*.*')]
            )
            if filename:
                try:
                    self.results_df.to_csv(filename, index=False)
                    self.log(f"Results exported to CSV: {filename}")
                    messagebox.showinfo("Success", f"Results exported to:\n{filename}")
                except Exception as e:
                    messagebox.showerror("Error", f"Export failed: {str(e)}")

def main():
    """Main function"""
    root = tk.Tk()
    app = ARECSimpleTreeCountGUI(root)
    root.mainloop()

if __name__ == '__main__':
    main() 
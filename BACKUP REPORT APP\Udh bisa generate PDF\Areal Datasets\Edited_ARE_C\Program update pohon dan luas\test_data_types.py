"""
Test script to verify data type handling fixes
This script tests the data type conversion and validation logic
"""

import geopandas as gpd
import pandas as pd
import os

def test_data_type_conversion():
    """Test data type conversion logic"""
    print("Testing Data Type Conversion...")
    print("=" * 40)
    
    boundary_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp"
    
    if not os.path.exists(boundary_path):
        print(f"❌ Boundary file not found: {boundary_path}")
        return False
    
    try:
        gdf = gpd.read_file(boundary_path)
        print(f"✅ Loaded {len(gdf)} boundary polygons")
        
        # Test column renaming
        print("\nTesting column renaming...")
        boundary_mappings = {
            'SUB_DIVISI': 'SUBDIVISI',
            'HCV_Catego': 'HCV',
            'Ju<PERSON>lah_Poh': 'JUMLAH_POH'
        }
        
        for old_name, new_name in boundary_mappings.items():
            if old_name in gdf.columns and new_name not in gdf.columns:
                gdf = gdf.rename(columns={old_name: new_name})
                print(f"  ✅ Renamed '{old_name}' to '{new_name}'")
        
        # Test data type cleaning
        print("\nTesting data type cleaning...")
        numeric_columns = ['JUMLAH_POH', 'LUAS_AUTO', 'luas_aut_1', 'total_incl', 'luas_netto', 'luas_asss']
        
        for col in numeric_columns:
            if col in gdf.columns:
                print(f"\nProcessing column: {col}")
                print(f"  Original dtype: {gdf[col].dtype}")
                print(f"  Sample values: {gdf[col].head(3).tolist()}")
                
                # Convert to numeric
                original_values = gdf[col].copy()
                gdf[col] = pd.to_numeric(gdf[col], errors='coerce').fillna(0)
                
                print(f"  New dtype: {gdf[col].dtype}")
                print(f"  Sample converted: {gdf[col].head(3).tolist()}")
                
                # Test sum operation
                try:
                    total = gdf[col].sum()
                    print(f"  ✅ Sum calculation works: {total}")
                except Exception as e:
                    print(f"  ❌ Sum calculation failed: {e}")
        
        # Test HCV string conversion
        if 'HCV' in gdf.columns:
            print(f"\nTesting HCV column...")
            gdf['HCV'] = gdf['HCV'].astype(str)
            print(f"  HCV dtype: {gdf['HCV'].dtype}")
            print(f"  Sample HCV values: {gdf['HCV'].head(3).tolist()}")
            
            # Test pattern matching
            boundary_count = len(gdf[gdf['HCV'].str.contains('Boundary', na=False)])
            inclave_count = len(gdf[gdf['HCV'].str.contains('inclave', case=False, na=False)])
            
            print(f"  ✅ Boundary pattern matching: {boundary_count} polygons")
            print(f"  ✅ Inclave pattern matching: {inclave_count} polygons")
        
        print("\n✅ All data type conversions successful!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def test_numeric_operations():
    """Test numeric operations on converted data"""
    print("\n\nTesting Numeric Operations...")
    print("=" * 40)
    
    # Create sample data with mixed types (similar to your data)
    sample_data = {
        'JUMLAH_POH': [378, '378', 1118, '1356', 3146, 'NULL', 4374],
        'LUAS_AUTO': [2.9, '2.9', 6.01, '13.9', 30.13, 'NULL', 34.86],
        'HCV': ['Boundary-29', 'Boundary-45', 'Boundary-11', 'Boundary-43', 'Boundary-49', 'Boundary-47', 'Boundary-13']
    }
    
    df = pd.DataFrame(sample_data)
    print("Sample data created:")
    print(df)
    
    print("\nTesting numeric conversion...")
    
    # Test JUMLAH_POH conversion
    print("\nJUMLAH_POH conversion:")
    print(f"  Original: {df['JUMLAH_POH'].tolist()}")
    df['JUMLAH_POH'] = pd.to_numeric(df['JUMLAH_POH'], errors='coerce').fillna(0)
    print(f"  Converted: {df['JUMLAH_POH'].tolist()}")
    
    try:
        total_trees = df['JUMLAH_POH'].sum()
        print(f"  ✅ Sum works: {total_trees}")
    except Exception as e:
        print(f"  ❌ Sum failed: {e}")
    
    # Test LUAS_AUTO conversion
    print("\nLUAS_AUTO conversion:")
    print(f"  Original: {df['LUAS_AUTO'].tolist()}")
    df['LUAS_AUTO'] = pd.to_numeric(df['LUAS_AUTO'], errors='coerce').fillna(0)
    print(f"  Converted: {df['LUAS_AUTO'].tolist()}")
    
    try:
        total_area = df['LUAS_AUTO'].sum()
        print(f"  ✅ Sum works: {total_area}")
    except Exception as e:
        print(f"  ❌ Sum failed: {e}")
    
    # Test HCV pattern matching
    print("\nHCV pattern matching:")
    df['HCV'] = df['HCV'].astype(str)
    boundary_mask = df['HCV'].str.contains('Boundary', na=False)
    boundary_count = boundary_mask.sum()
    print(f"  ✅ Boundary patterns found: {boundary_count}")
    
    return True

def main():
    """Run all tests"""
    print("Data Type Handling Test Suite")
    print("=" * 50)
    
    tests = [
        ("Data Type Conversion Test", test_data_type_conversion),
        ("Numeric Operations Test", test_numeric_operations)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Data type handling should work correctly.")
        print("The application should now handle mixed data types properly.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")

if __name__ == "__main__":
    main()

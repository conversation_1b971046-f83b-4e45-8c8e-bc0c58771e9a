import geopandas as gpd
import pandas as pd
import os

def remove_duplicates_by_area_and_count(input_path, output_path=None, area_col='luas_auto', count_col='JUMLAH_POH'):
    print(f"Membaca file: {input_path}")
    gdf = gpd.read_file(input_path)
    print(f"Jumlah fitur awal: {len(gdf)}")

    # Pastikan kolom area dan jumlah pohon ada
    if area_col not in gdf.columns:
        print(f"Kolom area '{area_col}' tidak ditemukan, menghitung area dari geometry...")
        gdf['__AREA__'] = gdf.geometry.area
        area_col = '__AREA__'
    if count_col not in gdf.columns:
        raise ValueError(f"Kolom '{count_col}' tidak ditemukan di shapefile!")

    # Hapus duplikat berdasarkan area dan jumlah pohon
    print(f"Menghapus duplikat berdasarkan area ('{area_col}') dan jumlah pohon ('{count_col}')...")
    gdf_clean = gdf.drop_duplicates(subset=[area_col, count_col])
    gdf_clean = gdf_clean.reset_index(drop=True)

    print(f"Jumlah fitur setelah dibersihkan: {len(gdf_clean)}")
    print(f"Duplikat yang dihapus: {len(gdf) - len(gdf_clean)}")

    # Simpan hasil
    if output_path is None:
        base, ext = os.path.splitext(input_path)
        output_path = base + '_cleaned.shp'
    gdf_clean.to_file(output_path)
    print(f"Hasil disimpan ke: {output_path}")

if __name__ == "__main__":
    input_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Polygon_ARE_C_dengan_Jumlah_Pohon.shp"
    output_path = r"D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Polygon_ARE_C_dengan_Jumlah_Pohon_cleaned.shp"
    remove_duplicates_by_area_and_count(input_path, output_path) 
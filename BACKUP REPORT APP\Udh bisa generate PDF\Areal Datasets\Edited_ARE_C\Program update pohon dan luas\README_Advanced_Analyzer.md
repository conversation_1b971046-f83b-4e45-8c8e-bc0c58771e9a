# ARE C Advanced Spatial Analyzer

## Overview
Comprehensive GUI application for palm tree detection analysis with advanced spatial processing capabilities for the ARE C project.

## Features

### Core Functionality
- **Spatial Overlay Analysis**: Precise point-in-polygon analysis between detection points and LSU boundaries
- **HCV Category Processing**: Handles HCV Category 0 (main boundaries) and HCV Category 1 (unplanted areas)
- **Advanced Polygon Hole Creation**: Creates geometric holes in larger LSU polygons where smaller LSUs exist inside them
- **Net Plantable Area Calculations**: Computes net plantable area = total LSU area - total unplanted area
- **Real-time Progress Tracking**: Live progress indicators and detailed logging

### GUI Interface
- **Data Management Tab**: File browser, data validation, and initial data loading
- **Spatial Analysis Tab**: Processing options, progress tracking, and analysis controls
- **Results Tab**: Summary statistics and detailed results table
- **Export Tab**: Multiple export formats (Excel, CSV, Shapefile, GeoJSON)
- **System Log Tab**: Comprehensive logging and debugging information

## Data Sources (Fixed Paths)
- **Boundary Polygon Shapefile**: `D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\ARE_C_HASIL_PERBAIKAN.shp`
- **Detection Points Shapefile**: `D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp`

## Installation

### Prerequisites
- Python 3.8 or higher
- Required Python packages (see requirements_advanced_analyzer.txt)

### Setup
1. Install required packages:
   ```bash
   pip install -r requirements_advanced_analyzer.txt
   ```

2. Run the application:
   ```bash
   python are_c_advanced_spatial_analyzer.py
   ```

## Usage

### 1. Data Management
- Launch the application
- Verify data sources in the "Data Management" tab
- Click "Validate Data" to check data integrity
- Review validation results in the log area

### 2. Spatial Analysis
- Switch to "Spatial Analysis" tab
- Select desired processing options:
  - ✓ Perform Spatial Overlay Analysis
  - ✓ Process HCV Categories
  - ✓ Calculate Net Plantable Areas
  - ☐ Create Polygon Holes (Advanced) - Optional
- Click "Start Analysis" to begin processing
- Monitor progress in real-time

### 3. View Results
- Switch to "Results" tab after analysis completion
- Review summary statistics
- Examine detailed results table
- Use "Refresh Results" to update display

### 4. Export Data
- Switch to "Export" tab
- Select desired export formats
- Choose output directory
- Click "Export All" for batch export or use individual export buttons

## Technical Specifications

### Spatial Processing
- **Coordinate System**: WGS 84 / UTM zone 48S (EPSG:32748)
- **Spatial Operations**: GeoPandas with Shapely for geometric operations
- **Overlay Method**: Spatial join with 'within' predicate
- **Area Calculations**: Automatic conversion to hectares

### HCV Category Logic
- **HCV=0**: Main LSU boundary/border areas (used for tree counting)
- **HCV=1**: Unplanted areas/enclaves within LSUs (subtracted from total area)
- **Grouping**: By Sub Division and Block combinations
- **Net Area**: Total LSU area minus total unplanted area

### Output Fields
- `BLOK`: Block identifier
- `SUBDIVISI`: Sub Division identifier
- `total_area_ha`: Total area in hectares
- `unplanted_area_ha`: Unplanted area in hectares
- `net_plantable_area_ha`: Net plantable area in hectares
- `tree_count`: Number of detected palm trees
- `JUMLAH_POH`: Updated tree count field in shapefile

## Error Handling
- Comprehensive data validation before processing
- Graceful handling of missing or corrupted spatial data
- Detailed error messages and logging
- Processing cancellation capability

## Logging
- Automatic log file creation in `logs/` directory
- Timestamped entries for audit trails
- Multiple log levels (INFO, WARNING, ERROR)
- Log file export functionality

## Export Formats
- **Excel (.xlsx)**: Summary and detailed data in separate sheets
- **CSV (.csv)**: Summary data in comma-separated format
- **Shapefile (.shp)**: Updated spatial data with all attributes
- **GeoJSON (.geojson)**: Web-compatible spatial format

## Performance Considerations
- Multi-threaded processing for responsive GUI
- Efficient spatial indexing for large datasets
- Memory-optimized operations for polygon processing
- Progress tracking for long-running operations

## Troubleshooting

### Common Issues
1. **File Not Found**: Verify shapefile paths in Data Management tab
2. **CRS Mismatch**: Application automatically handles CRS conversion
3. **Invalid Geometries**: Check validation log for geometry issues
4. **Memory Issues**: Process smaller datasets or increase system memory

### Support
- Check System Log tab for detailed error information
- Review validation results before processing
- Ensure all required dependencies are installed

## Version Information
- **Created**: 2025-07-03
- **Python Version**: 3.8+
- **GeoPandas Version**: 0.12.0+
- **GUI Framework**: Tkinter (built-in)

## Author
Generated for ARE C Analysis Project - Palm Tree Detection and Spatial Analysis

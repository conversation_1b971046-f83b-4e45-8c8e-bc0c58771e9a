# Analis<PERSON>hon per Blok

Script Python untuk menghitung jumlah titik deteksi pohon dalam setiap polygon blok menggunakan analisis spasial.

## Deskripsi

Script ini melakukan:
1. Membaca shapefile polygon blok yang dikelompokkan berdasarkan field BLOK
2. Membaca shapefile titik hasil deteksi pohon
3. Melakukan spatial join untuk menghitung jumlah pohon dalam setiap blok
4. Menambahkan field baru `JUMLAH_POHON` ke atribut tabel polygon
5. Menyimpan hasil ke shapefile baru

## Input Files

- **Polygon Blok**: `Polygon_Tambahan_ARE_C.shp` - Shapefile polygon dengan field BLOK
- **Titik Deteksi**: `ARE_C_All_Detection.shp` - Shapefile titik hasil deteksi pohon

## Output

- **Hasil**: `Polygon_ARE_C_dengan_Jumlah_Pohon.shp` - Shapefile polygon dengan field tambahan JUMLAH_POHON

## Instalasi

### 1. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 2. Atau Install Manual

```bash
pip install geopandas pandas shapely fiona pyproj
```

## Cara Penggunaan

### 1. Jalankan Script Langsung

```bash
python tree_count_analysis.py
```

Script akan otomatis menggunakan path yang sudah ditentukan.

### 2. Menggunakan sebagai Module

```python
from tree_count_analysis import count_trees_in_polygons

# Path ke file shapefile
polygon_path = "path/to/your/polygon.shp"
points_path = "path/to/your/points.shp"
output_path = "path/to/output.shp"

# Jalankan analisis
result = count_trees_in_polygons(polygon_path, points_path, output_path)
```

## Fitur Utama

- ✅ **Spatial Join**: Menghitung titik dalam polygon dengan akurat
- ✅ **CRS Handling**: Otomatis menyamakan sistem koordinat
- ✅ **Error Handling**: Menangani error dengan informasi yang jelas
- ✅ **Statistik**: Menampilkan ringkasan hasil analisis
- ✅ **Progress Info**: Menampilkan progress selama proses

## Output Statistik

Script akan menampilkan:
- Jumlah pohon per blok
- Total pohon keseluruhan
- Rata-rata pohon per blok
- Blok dengan pohon terbanyak/tersedikit

## Struktur Field Output

Shapefile hasil akan memiliki field tambahan:
- `JUMLAH_POHON` (Integer): Jumlah titik pohon dalam polygon

## Troubleshooting

### Error: File tidak ditemukan
- Pastikan path file shapefile benar
- Periksa apakah file .shp, .shx, .dbf, .prj ada lengkap

### Error: CRS tidak kompatibel
- Script otomatis menyamakan CRS
- Pastikan kedua file memiliki informasi proyeksi

### Error: Kolom BLOK tidak ditemukan
- Periksa nama field di shapefile polygon
- Edit script jika nama field berbeda

## Requirements

- Python 3.7+
- geopandas
- pandas
- shapely
- fiona
- pyproj

## Path Default

Script menggunakan path berikut secara default:

```
Polygon: D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Polygon_Tambahan_ARE_C.shp

Points: D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\ARE_C_All_Detection.shp
```

Edit path dalam script jika lokasi file berbeda. 